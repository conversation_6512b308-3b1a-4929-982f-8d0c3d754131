# crealife_seglobes

crealife肺部分叶分割算法。

[TOC]

## 接口说明

+ ### 命令行调用

    命令示例:

    ```bash
    $ ...ndsb120/python -m crealife_seglobes.run -i inputvol -o outvol [-s True] [-e]
    ```

     -i INPUT_PATH, --input_path INPUT_PATH
                输入volume文件路径
     -o OUTPUT_PATH, --output_path OUTPUT_PATH
                输出mask volume文件路径
     -ss STEP_SIZE, --step_size STEP_SIZE
                step size
     -s SPEED, --speed SPEED
                启用快速模式，速度快，锯齿较明显，可用于快速定位；否则结果精细，耗时有所增加（默认False）


+ ### Python Backend Usage

    ```python
    from crealife_seglobes.api import segment_from_file
    from crealife_seglobes.api import segment_from_image

    input_image_path = 'path/to/input/image.nii.gz_OR_image.vol'
    output_mask_path = 'path/to/output/mask.vol'

    params_dict = {
        'step_size': 0.96, 	 # 步长[0.5~1.0]（越大越快，越小越仔细）
        'speed': False  	 # 快速模式
    }

    # 调用segment_from_file函数进行图像分割
    segment_from_file(input_image_path, output_mask_path, params_dict)

    # 调用segment_from_image进行分割
    mask_img = segment_from_image(sitkimg, params_dict)
    ```

+ 结果在DBVolume的m_pixel属性内。解析示例如下。

    ```c++
    DBVolume* preadvol = new DBVolume();
    preadvol->LoadDBVolume(outvol);
    int len = 0;
    BYTE* pbuf = preadvol->GetPixelData();
    // ...mask体数据

    delete preadvol;
    ```

+ Label说明

    label_code: 1, 2, 3, 4, 5,6

    ```json
     0：背景
     1：RU——"右上叶"
     2：RM——"右中叶"
     3：RL——"右下叶"
     4：LU——"左上叶"
     5：LL——"左下叶"
     6:  气管
    ```

---------------------------

## 环境配置

如下方法，即可完成配置。

**方法一：**

可通过如下命令直接安装最新版本

```bash
ndsb120/python -m pip install crealife_seglobes -U -i http://***********:8081/repository/crealife-pypi/simple --trusted-host ***********
```

安装指定版本，如下命令，将 `==0.2.1`替换为`==期望版本`即可。

```
ndsb120/python -m pip install crealife_seglobes==0.2.1 -i http://***********:8081/repository/crealife-pypi/simple --trusted-host ***********
```

**方法二（to be deprecated）:**

采用配置工具DeployTools.exe。  (工具所在目录：`\\***********\ProjectDeploy\PythonPackages\部署辅助工具`或`\\***********\folder for yaohy\PythonPackages\部署辅助工具`)

选择ndsb（python.exe）目录

选择安装包

+ ` crealife_seglobes-0.2.1-py3-none-any.whl`

进行安装。

[**若未安装**`crealife_volume`]增加此步

选择 `crealife_volume-0.7.4-py3-none-any.whl`进行安装。
