import pprint
import subprocess
import sys

import SimpleITK as sitk

# from cls_lung import slice_lung_layers
#
# itkimg= sitk.ReadImage(r"D:\WorkSpace\git_repository\src_seglobes\sitkimg.nii.gz")
# res = slice_lung_layers(itkimg, use_trt=True)
# print(res[1], res[2])

files = [
    r'O:\LungNodule\saturn_20240311\imagesTr\190332.imgvol',
    r'O:\LungNodule\saturn_20240311\imagesTr\33279.imgvol',
    r'O:\LungNodule\saturn_20240311\imagesVl\1239515.imgvol'
]

for file in files:
    inp = file
    out = r"D:\tmp\out.vol"
    cmds = f'{sys.executable} -m crealife_seglobes.run -i {inp} -o {out} -s True'
    ret = subprocess.Popen(cmds, shell=True,
                           stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding="utf-8").communicate()
    pprint.pprint(ret)
