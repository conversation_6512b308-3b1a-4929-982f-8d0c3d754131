import os.path
import shutil
import sys
import tempfile
from datetime import datetime
from time import time

from pdutils.utils import default_parser

from crealife_seglobes import __version__
from crealife_seglobes.api import segment_from_file
from crealife_seglobes.utils import str2bool


def parse_args():
    parser = default_parser()
    parser.add_argument('-p', '--post', dest='postprocess',
                        help='Execute Postprocess', default=True, type=str2bool)
    parser.add_argument('-c', '--chk', help='the checkpoint model.',
                        default=4, type=int)
    parser.add_argument('-t', '--type',
                        help='data type. 0 is dbvolume, 1 is nii(.gz)', default=0, type=int)
    parser.add_argument('--nii', dest='is_nii',
                        help='is nii(.gz)', default=False, type=str2bool)
    parser.add_argument('-s', '--speed',
                        help='Speed takes precedence', default=False, type=str2bool)
    parser.add_argument(
        '-ss', '--step_size', type=float, default=0.95,
        help='step size'
    )
    parser.add_argument('--use_trt', type=str2bool, default=True, help='use trt')
    parser.add_argument(
        '--debug', default=False, type=str2bool, help='debug mode'
    )
    return parser.parse_args()


def main():
    args = parse_args()
    tmpdir = tempfile.gettempdir()
    now = datetime.now()
    current_time = now.strftime('%Y%m%d%H%M%S')
    if args.debug:
        tmp_file = os.path.join(tmpdir, f'seglobes_{current_time}.log')
        with open(tmp_file, 'w', encoding='utf-8') as tmpf:
            tmpf.write(f'{__version__ =}\n')
            tmpf.write(f'{sys.argv}')
        shutil.copy(args.input_path, os.path.join(tmpdir, f'seglobes_{current_time}_in.vol'))
    t0 = time()
    segment_from_file(args.input_path, args.output_path, args.__dict__)
    print(f'total {time() - t0:.2f}s')


if __name__ == '__main__':
    main()
