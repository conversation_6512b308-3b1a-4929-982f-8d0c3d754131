"""
ONNX model inference implementation.

This module provides ONNX Runtime-based model inference functionality.
"""

import os
from typing import Dict, Any, List, Optional
import numpy as np

from .base_inference import GenericModelInference


class ONNXModelInference(GenericModelInference):
    """ONNX model inference using ONNX Runtime."""
    
    def __init__(self, **kwargs):
        """
        Initialize ONNX model inference.
        
        Args:
            **kwargs: Configuration parameters including:
                - providers: List of execution providers
                - session_options: ONNX Runtime session options
        """
        super().__init__(**kwargs)
        
        self.providers = kwargs.get('providers', ['CUDAExecutionProvider', 'CPUExecutionProvider'])
        self.session_options = kwargs.get('session_options', None)
        self.session = None
        
        # Try to import ONNX Runtime
        try:
            import onnxruntime as ort
            self.ort = ort
            self.onnx_available = True
        except ImportError:
            self.onnx_available = False
            raise ImportError("ONNX Runtime not available. Please install onnxruntime or onnxruntime-gpu.")
    
    def _load_model_implementation(self, **kwargs) -> None:
        """
        Load ONNX model implementation.
        
        Args:
            **kwargs: Additional parameters
        """
        if not self.onnx_available:
            raise RuntimeError("ONNX Runtime not available")
        
        # Create session options if not provided
        if self.session_options is None:
            self.session_options = self.ort.SessionOptions()
            self.session_options.graph_optimization_level = self.ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        # Create inference session
        try:
            self.session = self.ort.InferenceSession(
                self.model_path,
                sess_options=self.session_options,
                providers=self.providers
            )
            
            # Get input/output information
            self._get_model_io_info()
            
            print(f"ONNX model loaded successfully. Providers: {self.session.get_providers()}")
            
        except Exception as e:
            raise RuntimeError(f"Failed to load ONNX model: {e}")
    
    def _get_model_io_info(self) -> None:
        """Get model input/output information."""
        if self.session is None:
            return
        
        # Get input information
        self.input_details = []
        for input_meta in self.session.get_inputs():
            self.input_details.append({
                'name': input_meta.name,
                'shape': input_meta.shape,
                'type': input_meta.type
            })
        
        # Get output information
        self.output_details = []
        for output_meta in self.session.get_outputs():
            self.output_details.append({
                'name': output_meta.name,
                'shape': output_meta.shape,
                'type': output_meta.type
            })
        
        # Update tensor names if not specified
        if not self.input_name and self.input_details:
            self.input_name = self.input_details[0]['name']
        
        if not self.output_name and self.output_details:
            self.output_name = self.output_details[0]['name']
    
    def _predict_implementation(self, image: np.ndarray, **kwargs) -> np.ndarray:
        """
        ONNX prediction implementation.
        
        Args:
            image: Input image array
            **kwargs: Additional parameters
            
        Returns:
            Model prediction result
        """
        if self.session is None:
            raise RuntimeError("ONNX session not initialized")
        
        # Prepare input
        input_data = {self.input_name: image}
        
        # Run inference
        try:
            outputs = self.session.run([self.output_name], input_data)
            prediction = outputs[0]
            
            return prediction
            
        except Exception as e:
            raise RuntimeError(f"ONNX inference failed: {e}")
    
    def run_with_multiple_outputs(self, image: np.ndarray, 
                                 output_names: Optional[List[str]] = None) -> List[np.ndarray]:
        """
        Run inference with multiple outputs.
        
        Args:
            image: Input image array
            output_names: List of output names (uses all outputs if None)
            
        Returns:
            List of output arrays
        """
        if self.session is None:
            raise RuntimeError("ONNX session not initialized")
        
        if output_names is None:
            output_names = [detail['name'] for detail in self.output_details]
        
        # Prepare input
        input_data = {self.input_name: image}
        
        # Run inference
        try:
            outputs = self.session.run(output_names, input_data)
            return outputs
            
        except Exception as e:
            raise RuntimeError(f"ONNX inference failed: {e}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get ONNX model information.
        
        Returns:
            Dictionary containing model information
        """
        info = super().get_model_info()
        info.update({
            'backend': 'ONNX Runtime',
            'providers': self.providers,
            'input_details': getattr(self, 'input_details', []),
            'output_details': getattr(self, 'output_details', [])
        })
        return info
    
    def optimize_for_inference(self) -> None:
        """Optimize model for inference."""
        if self.session_options is None:
            self.session_options = self.ort.SessionOptions()
        
        # Enable optimizations
        self.session_options.graph_optimization_level = self.ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        self.session_options.execution_mode = self.ort.ExecutionMode.ORT_SEQUENTIAL
        
        # Enable memory pattern optimization
        self.session_options.enable_mem_pattern = True
        
        # Set number of threads for CPU execution
        if 'CPUExecutionProvider' in self.providers:
            self.session_options.intra_op_num_threads = os.cpu_count()
    
    def cleanup(self) -> None:
        """Clean up ONNX session resources."""
        if self.session is not None:
            del self.session
            self.session = None
        super().cleanup()


class Model:
    """
    Wrapper class for ONNX model to maintain compatibility with original code.
    
    This class provides the same interface as the original Model class.
    """
    
    def __init__(self, config: Dict[str, Any], pkg_path: str = ''):
        """
        Initialize model wrapper.
        
        Args:
            config: Model configuration
            pkg_path: Package path containing models
        """
        self.config = config
        self.pkg_path = pkg_path
        
        # Build model path
        model_name = config.get('name', '')
        self.model_path = os.path.join(pkg_path, f'{model_name}.onnx')
        
        # Initialize ONNX inference
        self.inference = ONNXModelInference(
            model_path=self.model_path,
            input_tensor=config.get('input_tensor', {}),
            output_tensor=config.get('output_tensor', {}),
            **config
        )
        
        # Load model
        if os.path.exists(self.model_path):
            self.inference.load_model(self.model_path)
            self.model = self.inference.session
            self.t_in = self.inference.input_name
            self.t_ot = [self.inference.output_name]
        else:
            raise FileNotFoundError(f"Model file not found: {self.model_path}")
    
    def gpu(self):
        """Context manager for GPU execution (compatibility method)."""
        return self
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
