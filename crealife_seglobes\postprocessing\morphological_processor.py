"""
Morphological postprocessor.

This module provides morphological operations for segmentation postprocessing,
including erosion, dilation, opening, closing, and advanced morphological operations.
"""

from typing import Dict, Any, Optional, Tuple, Union
import numpy as np
from scipy import ndimage
from scipy.ndimage import binary_erosion, binary_dilation, binary_opening, binary_closing

from .base_postprocessor import GenericPostprocessor


class MorphologicalPostprocessor(GenericPostprocessor):
    """Postprocessor specialized for morphological operations."""
    
    def __init__(self, **kwargs):
        """
        Initialize morphological postprocessor.
        
        Args:
            **kwargs: Configuration parameters including:
                - structuring_element: Type of structuring element ('ball', 'cube', 'cross')
                - element_size: Size of structuring element
                - operations_sequence: Sequence of operations to apply
                - custom_kernels: Dictionary of custom kernels for operations
        """
        super().__init__(**kwargs)
        
        self.structuring_element_type = kwargs.get('structuring_element', 'ball')
        self.element_size = kwargs.get('element_size', 1)
        self.operations_sequence = kwargs.get('operations_sequence', [])
        self.custom_kernels = kwargs.get('custom_kernels', {})
        
        # Create default structuring element
        self.default_structure = self._create_structuring_element(
            self.structuring_element_type, 
            self.element_size
        )
    
    def postprocess(self, mask: np.ndarray, **kwargs) -> np.ndarray:
        """
        Postprocess mask using morphological operations.
        
        Args:
            mask: Input segmentation mask
            **kwargs: Additional parameters
            
        Returns:
            Postprocessed mask
        """
        if mask.size == 0:
            return mask
        
        processed = mask.copy()
        
        # Apply morphological operations sequence
        if self.operations_sequence:
            processed = self.apply_operations_sequence(processed, self.operations_sequence)
        
        # Apply base postprocessing
        processed = super().postprocess(processed, **kwargs)
        
        return processed
    
    def apply_operations_sequence(self, mask: np.ndarray, operations: list) -> np.ndarray:
        """
        Apply a sequence of morphological operations.
        
        Args:
            mask: Input mask
            operations: List of operation specifications
            
        Returns:
            Processed mask
        """
        processed = mask.copy()
        
        for operation in operations:
            if isinstance(operation, str):
                # Simple operation name
                processed = self.apply_single_operation(processed, operation)
            elif isinstance(operation, dict):
                # Operation with parameters
                op_name = operation.get('name', '')
                op_params = {k: v for k, v in operation.items() if k != 'name'}
                processed = self.apply_single_operation(processed, op_name, **op_params)
        
        return processed
    
    def apply_single_operation(self, mask: np.ndarray, operation: str, **kwargs) -> np.ndarray:
        """
        Apply a single morphological operation.
        
        Args:
            mask: Input mask
            operation: Operation name
            **kwargs: Operation parameters
            
        Returns:
            Processed mask
        """
        # Get structuring element
        structure = kwargs.get('structure', self.default_structure)
        iterations = kwargs.get('iterations', 1)
        
        if operation == 'erosion':
            return self.erosion(mask, structure, iterations)
        elif operation == 'dilation':
            return self.dilation(mask, structure, iterations)
        elif operation == 'opening':
            return self.opening(mask, structure, iterations)
        elif operation == 'closing':
            return self.closing(mask, structure, iterations)
        elif operation == 'gradient':
            return self.morphological_gradient(mask, structure)
        elif operation == 'tophat':
            return self.top_hat(mask, structure)
        elif operation == 'blackhat':
            return self.black_hat(mask, structure)
        elif operation == 'fill_holes':
            return self.fill_holes_morphological(mask)
        elif operation == 'remove_small_holes':
            hole_size = kwargs.get('hole_size', 64)
            return self.remove_small_holes(mask, hole_size)
        else:
            print(f"Unknown morphological operation: {operation}")
            return mask
    
    def erosion(self, mask: np.ndarray, structure: Optional[np.ndarray] = None, 
                iterations: int = 1) -> np.ndarray:
        """
        Apply morphological erosion.
        
        Args:
            mask: Input mask
            structure: Structuring element
            iterations: Number of iterations
            
        Returns:
            Eroded mask
        """
        if structure is None:
            structure = self.default_structure
        
        if mask.max() > 1:
            # Multi-label mask
            processed = np.zeros_like(mask)
            for label in range(1, mask.max() + 1):
                binary_mask = (mask == label)
                eroded = binary_erosion(binary_mask, structure, iterations=iterations)
                processed[eroded] = label
            return processed
        else:
            # Binary mask
            return binary_erosion(mask, structure, iterations=iterations).astype(mask.dtype)
    
    def dilation(self, mask: np.ndarray, structure: Optional[np.ndarray] = None, 
                 iterations: int = 1) -> np.ndarray:
        """
        Apply morphological dilation.
        
        Args:
            mask: Input mask
            structure: Structuring element
            iterations: Number of iterations
            
        Returns:
            Dilated mask
        """
        if structure is None:
            structure = self.default_structure
        
        if mask.max() > 1:
            # Multi-label mask - handle label conflicts
            processed = np.zeros_like(mask)
            for label in range(1, mask.max() + 1):
                binary_mask = (mask == label)
                dilated = binary_dilation(binary_mask, structure, iterations=iterations)
                # Only add pixels that are not already occupied by other labels
                new_pixels = dilated & (processed == 0)
                processed[new_pixels] = label
            return processed
        else:
            # Binary mask
            return binary_dilation(mask, structure, iterations=iterations).astype(mask.dtype)
    
    def opening(self, mask: np.ndarray, structure: Optional[np.ndarray] = None, 
                iterations: int = 1) -> np.ndarray:
        """
        Apply morphological opening (erosion followed by dilation).
        
        Args:
            mask: Input mask
            structure: Structuring element
            iterations: Number of iterations
            
        Returns:
            Opened mask
        """
        if structure is None:
            structure = self.default_structure
        
        if mask.max() > 1:
            # Multi-label mask
            processed = np.zeros_like(mask)
            for label in range(1, mask.max() + 1):
                binary_mask = (mask == label)
                opened = binary_opening(binary_mask, structure, iterations=iterations)
                processed[opened] = label
            return processed
        else:
            # Binary mask
            return binary_opening(mask, structure, iterations=iterations).astype(mask.dtype)
    
    def closing(self, mask: np.ndarray, structure: Optional[np.ndarray] = None, 
                iterations: int = 1) -> np.ndarray:
        """
        Apply morphological closing (dilation followed by erosion).
        
        Args:
            mask: Input mask
            structure: Structuring element
            iterations: Number of iterations
            
        Returns:
            Closed mask
        """
        if structure is None:
            structure = self.default_structure
        
        if mask.max() > 1:
            # Multi-label mask
            processed = np.zeros_like(mask)
            for label in range(1, mask.max() + 1):
                binary_mask = (mask == label)
                closed = binary_closing(binary_mask, structure, iterations=iterations)
                processed[closed] = label
            return processed
        else:
            # Binary mask
            return binary_closing(mask, structure, iterations=iterations).astype(mask.dtype)
    
    def morphological_gradient(self, mask: np.ndarray, 
                              structure: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Apply morphological gradient (dilation - erosion).
        
        Args:
            mask: Input mask
            structure: Structuring element
            
        Returns:
            Gradient mask
        """
        if structure is None:
            structure = self.default_structure
        
        dilated = self.dilation(mask, structure)
        eroded = self.erosion(mask, structure)
        
        return (dilated.astype(int) - eroded.astype(int)).clip(0, mask.max()).astype(mask.dtype)
    
    def top_hat(self, mask: np.ndarray, structure: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Apply top-hat transform (original - opening).
        
        Args:
            mask: Input mask
            structure: Structuring element
            
        Returns:
            Top-hat transformed mask
        """
        if structure is None:
            structure = self.default_structure
        
        opened = self.opening(mask, structure)
        return (mask.astype(int) - opened.astype(int)).clip(0, mask.max()).astype(mask.dtype)
    
    def black_hat(self, mask: np.ndarray, structure: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Apply black-hat transform (closing - original).
        
        Args:
            mask: Input mask
            structure: Structuring element
            
        Returns:
            Black-hat transformed mask
        """
        if structure is None:
            structure = self.default_structure
        
        closed = self.closing(mask, structure)
        return (closed.astype(int) - mask.astype(int)).clip(0, mask.max()).astype(mask.dtype)
    
    def fill_holes_morphological(self, mask: np.ndarray) -> np.ndarray:
        """
        Fill holes using morphological operations.
        
        Args:
            mask: Input mask
            
        Returns:
            Mask with holes filled
        """
        return self.fill_holes_in_mask(mask)
    
    def remove_small_holes(self, mask: np.ndarray, hole_size: int = 64) -> np.ndarray:
        """
        Remove small holes from mask.
        
        Args:
            mask: Input mask
            hole_size: Maximum size of holes to remove
            
        Returns:
            Mask with small holes removed
        """
        if mask.max() > 1:
            # Multi-label mask
            processed = mask.copy()
            for label in range(1, mask.max() + 1):
                binary_mask = (mask == label)
                processed_binary = self._remove_small_holes_binary(binary_mask, hole_size)
                processed[mask == label] = 0
                processed[processed_binary] = label
            return processed
        else:
            # Binary mask
            return self._remove_small_holes_binary(mask.astype(bool), hole_size).astype(mask.dtype)
    
    def _remove_small_holes_binary(self, binary_mask: np.ndarray, hole_size: int) -> np.ndarray:
        """
        Remove small holes from binary mask.
        
        Args:
            binary_mask: Binary input mask
            hole_size: Maximum hole size to remove
            
        Returns:
            Binary mask with small holes removed
        """
        try:
            from skimage.morphology import remove_small_holes
            return remove_small_holes(binary_mask, area_threshold=hole_size)
        except ImportError:
            # Fallback implementation
            filled = ndimage.binary_fill_holes(binary_mask)
            holes = filled & ~binary_mask
            
            # Label holes and remove small ones
            labeled_holes, num_holes = ndimage.label(holes)
            hole_sizes = ndimage.sum(holes, labeled_holes, range(1, num_holes + 1))
            
            small_holes = np.zeros_like(holes)
            for i, size in enumerate(hole_sizes):
                if size <= hole_size:
                    small_holes |= (labeled_holes == i + 1)
            
            return binary_mask | small_holes
    
    def _create_structuring_element(self, element_type: str, size: int) -> np.ndarray:
        """
        Create structuring element.
        
        Args:
            element_type: Type of element ('ball', 'cube', 'cross')
            size: Size of element
            
        Returns:
            Structuring element array
        """
        if element_type == 'ball':
            return self._create_ball_element(size)
        elif element_type == 'cube':
            return np.ones((size, size, size), dtype=bool)
        elif element_type == 'cross':
            return self._create_cross_element(size)
        else:
            # Default to ball
            return self._create_ball_element(size)
    
    def _create_ball_element(self, radius: int) -> np.ndarray:
        """Create spherical structuring element."""
        size = 2 * radius + 1
        center = radius
        
        element = np.zeros((size, size, size), dtype=bool)
        
        for z in range(size):
            for y in range(size):
                for x in range(size):
                    distance = np.sqrt((x - center)**2 + (y - center)**2 + (z - center)**2)
                    if distance <= radius:
                        element[z, y, x] = True
        
        return element
    
    def _create_cross_element(self, size: int) -> np.ndarray:
        """Create cross-shaped structuring element."""
        element = np.zeros((size, size, size), dtype=bool)
        center = size // 2
        
        # Create cross in each dimension
        element[center, :, center] = True  # Y axis
        element[:, center, center] = True  # Z axis
        element[center, center, :] = True  # X axis
        
        return element
    
    def get_morphological_info(self) -> Dict[str, Any]:
        """
        Get morphological processor information.
        
        Returns:
            Dictionary containing processor parameters
        """
        info = self.get_postprocessing_info()
        info.update({
            'processor_type': 'morphological',
            'structuring_element_type': self.structuring_element_type,
            'element_size': self.element_size,
            'operations_sequence': self.operations_sequence,
            'custom_kernels': list(self.custom_kernels.keys())
        })
        return info
