import unittest

import numpy as np

from crealife_seglobes.cc_analysis import (_keep_largest_cc_every_label,
                                           keep_largest_connected_component)


class TestKeepLargestCCEveryLabel(unittest.TestCase):
    def test_empty_mask(self):
        """
        测试空掩码
        """
        mask = np.array([])
        res_mask = _keep_largest_cc_every_label(mask)
        np.testing.assert_array_equal(res_mask, np.array([]))

    def test_single_label(self):
        """
        测试单个标签
        """
        mask = np.array([[1, 1, 1], [1, 1, 1], [1, 1, 1]])
        expected_mask = np.array([[1, 1, 1], [1, 1, 1], [1, 1, 1]])
        res = _keep_largest_cc_every_label(mask)
        np.testing.assert_array_equal(res, expected_mask)

    def test_no_labels(self):
        """
        测试没有标签
        """
        mask = np.array([[0, 0, 0], [0, 0, 0], [0, 0, 0]])
        expected_mask = np.array([[0, 0, 0], [0, 0, 0], [0, 0, 0]])
        _keep_largest_cc_every_label(mask)
        np.testing.assert_array_equal(mask, expected_mask)

    def test_multiple_labels(self):
        """
        测试多个标签
        """
        mask = np.array([[2, 1, 0], [0, 2, 2], [3, 3, 3]])
        expected_mask = np.array([[0, 1, 0], [0, 2, 2], [3, 3, 3]])
        _keep_largest_cc_every_label(mask)
        np.testing.assert_array_equal(mask, expected_mask)

    def test_multiple_labels_has_no_features(self):
        """
        测试多个标签, 没有对应label
        """
        mask = np.array([[2, 1, 0], [0, 2, 2], [4, 4, 4]])
        expected_mask = np.array([[0, 1, 0], [0, 2, 2], [4, 4, 4]])
        _keep_largest_cc_every_label(mask)
        np.testing.assert_array_equal(mask, expected_mask)

    def test_keep_largest_no_labels(self):
        """
        测试没有标签
        """
        mask = np.array([[0, 0, 0], [0, 0, 0], [0, 0, 0]])
        expected_mask = np.array([[0, 0, 0], [0, 0, 0], [0, 0, 0]]).astype(bool)
        cm = keep_largest_connected_component(mask, 1)
        np.testing.assert_array_equal(cm, expected_mask)

        expected_mask = np.array([[1, 1, 1], [1, 1, 1], [1, 1, 1]]).astype(bool)
        cm = keep_largest_connected_component(mask, 0)
        np.testing.assert_array_equal(cm, expected_mask)

    def test_keep_largest_multiple_labels(self):
        """
        测试多个标签
        """
        mask = np.array([[2, 1, 0], [0, 2, 2], [3, 3, 3]])
        expected_1 = np.array([[0, 1, 0], [0, 0, 0], [0, 0, 0]]).astype(bool)
        expected_2 = np.array([[0, 0, 0], [0, 1, 1], [0, 0, 0]]).astype(bool)
        expected_3 = np.array([[0, 0, 0], [0, 0, 0], [1, 1, 1]]).astype(bool)

        res1 = keep_largest_connected_component(mask, 1)
        np.testing.assert_array_equal(res1, expected_1)
        res2 = keep_largest_connected_component(mask, 2)
        np.testing.assert_array_equal(res2, expected_2)
        res3 = keep_largest_connected_component(mask, 3)
        np.testing.assert_array_equal(res3, expected_3)
