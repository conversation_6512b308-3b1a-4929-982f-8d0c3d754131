import os
import queue
import threading
from functools import partial
from itertools import product

import numpy as np
from batchgenerators.augmentations.utils import pad_nd_image
from cuda import cudart
from pdutils.cuda import cuda_call
from pdutils.model.ox import OXModel as Model
from pdutils.nnunet.am import Adapter
from pdutils.nnunet.trt import TRTAdapter
from pdutils.nnunet.utils import compute_steps_for_sliding_window, get_gaussian
from pdutils.norm import nmlsm

from .constants import check_trt_env
from .utils import str2bool

os.environ['ONNX_DEV'] = '1'


def mem_free_device(device_ptr: int) -> None:
    cuda_call(cudart.cudaFree(device_ptr))


class SegModel(Adapter, TRTAdapter):
    def __init__(self, **kwargs):
        cfg = kwargs
        self.cfg = cfg
        cfl = isinstance(self.cfg, list)
        inten = cfg['intensities']
        self.intensities = (inten['mean'], inten['sd'], inten['lower'], inten['upper'])
        self.num_classes = cfg['num_classes']
        self.patch_size = cfg['patch_size']
        self.step_size = kwargs.get('step_size', 0.95)
        self.use_gaussian = True
        self.batch_size = 1

        trt_path = kwargs.get('trt_path', '')
        self.use_trt = str2bool(kwargs.get('use_trt', True))
        if trt_path and os.path.exists(trt_path) and check_trt_env() and self.use_trt:
            self.load(trt_path, num_classes=self.num_classes)
            self.prepare(self.patch_size)
            self.use_trt = True
            print(f'trt....step_size:{self.step_size}')
        else:
            print(f'onnx....step_size:{self.step_size}')
            pkg = kwargs.get('pkg', '')
            args = (pkg,)
            self.ox_model = [Model(c, *args) for c in self.cfg] if cfl else Model(self.cfg, *args)
            self.use_trt = False

    def _predict(self, x: np.ndarray) -> np.ndarray:
        # predict a block
        print(f"_predict called with shape: {x.shape}")
        if self.use_trt:
            print("Using TensorRT prediction")
            return TRTAdapter._predict(self, x)
        else:
            print("Using ONNX prediction")
            result = self.ox_model.model.run(self.ox_model.t_ot, {self.ox_model.t_in: x})[0]
            print(f"ONNX prediction completed, result shape: {result.shape}")
            # Remove batch dimension: (1, 7, 128, 160, 160) -> (7, 128, 160, 160)
            if result.shape[0] == 1:
                result = result[0]
            print(f"Final result shape: {result.shape}")
            return result

    def __call__(self, img: np.ndarray) -> np.ndarray:
        imgn = self.prep(img)
        if self.use_trt:
            prd = self._pred(imgn, bs=self.batch_size)
        else:
            with self.ox_model.gpu():
                prd = self._pred(imgn, bs=self.batch_size)
        seg = self.post(prd)
        return seg

    def prep(self, img: np.ndarray) -> np.ndarray:
        imgn = np.float32(img)
        imgn = nmlsm(imgn, *self.intensities)
        return imgn[None]  # type: ignore

    @staticmethod
    def post(prd: np.ndarray) -> np.ndarray:
        return prd.argmax(0).astype(np.uint8)

    def _pred(self, x, bs=1):
        import time
        pred_start = time.time()

        print(x.shape)
        data, slicer = pad_nd_image(x, self.patch_size, 'constant',
                                    None, True, None)
        # Fix: data.shape is (1, 1, D, H, W), we need (D, H, W) for compute_steps_for_sliding_window
        spatial_shape = data.shape[2:]  # Skip batch and channel dimensions
        steps = compute_steps_for_sliding_window(self.patch_size, spatial_shape, self.step_size)
        print(data.shape)
        num_tiles = len(steps[0]) * len(steps[1]) * len(steps[2])
        print(steps)
        print('num_tiles:', num_tiles)
        gaussian = get_gaussian(self.patch_size) if self.use_gaussian and num_tiles > 1 else None
        # data.shape = (1, 1, D, H, W), we want (classes, D, H, W) and (1, D, H, W)
        spatial_shape = data.shape[2:]  # (D, H, W)
        predicted_logits = np.zeros((self.num_classes,) + spatial_shape, dtype=np.float32)
        n_predictions = np.zeros((1,) + spatial_shape, dtype=np.float32)

        q = queue.Queue()

        def _combine_result(ids, predicted_logits, n_predictions, gaussian):
            sl, prediction = ids
            predicted_logits[sl] += prediction[0] * gaussian if gaussian is not None else prediction[0]
            n_predictions[sl] += gaussian if gaussian is not None else 1

        _cr_func = partial(
            _combine_result,
            predicted_logits=predicted_logits,
            n_predictions=n_predictions,
            gaussian=gaussian)

        def combine_writer():
            while True:
                item = q.get()
                if item is None:
                    break
                (slice_id, prediction) = item
                _cr_func((slice_id, prediction))
                del prediction
                q.task_done()

        writer_thread = threading.Thread(target=combine_writer)
        writer_thread.start()

        def infer_block(sl_id, data, q):
            # Extract patch from data - data is (1, 1, D, H, W)
            patch = data[sl_id]  # Extract patch

            # Ensure patch has correct channel dimension
            if patch.shape[1] == 0:  # Fix zero channel dimension
                patch = patch[:, :1, ...]  # Force at least 1 channel

            # Ensure patch has exact patch_size dimensions
            pd, ph, pw = self.patch_size

            # Crop to patch_size if larger
            patch = patch[:, :, :pd, :ph, :pw]

            # Pad if smaller (safety check)
            if patch.shape[2:] != (pd, ph, pw):
                import numpy as np
                pad_d = pd - patch.shape[2]
                pad_h = ph - patch.shape[3]
                pad_w = pw - patch.shape[4]
                patch = np.pad(patch, ((0,0), (0,0), (0,max(0,pad_d)), (0,max(0,pad_h)), (0,max(0,pad_w))), 'constant')

            block_res = self._predict(patch)
            q.put((sl_id, block_res))

        _infer_func = partial(infer_block, data=data, q=q)

        # 计算所有可能的切片
        print(f"Processing {num_tiles} tiles...")
        tile_count = 0
        for s in product(*steps):
            sl = (slice(None),) + tuple(slice(s[i], s[i] + self.patch_size[i]) for i in range(3))
            tile_count += 1
            print(f"Processing tile {tile_count}/{num_tiles}")
            _infer_func(sl)

        print("Waiting for all inference tasks to complete...")
        # 等待所有推理任务完成
        q.join()

        print("Terminating writer thread...")
        # 发送终止信号并等待writer线程结束
        q.put(None)
        writer_thread.join()
        print("All tiles processed successfully!")
        # 如下操作有问题！！线程内数据竞争，step_size < 0.8比较明显
        # params_list2 = [(sl, prediction) for sl, prediction in zip(sl_list, prediction_s)]
        # with ThreadPoolExecutor(4) as pool:
        #     _ = pool.map(_cr_func, params_list2)

        slicer = tuple([slice(None), *slicer[1:]])
        predicted_logits = predicted_logits[slicer]
        n_predictions = n_predictions[slicer]
        predicted_logits /= n_predictions

        pred_time = time.time() - pred_start
        print(f'Prediction time: {pred_time:.2f}s, Speed: {num_tiles/pred_time:.2f}it/s')

        return predicted_logits

    def __del__(self):
        if self.use_trt:
            self.trt_engine.__del__()
            self.trt_context.__del__()
            del self.inputs
            del self.outputs
            for _alloc in self.allocations:
                mem_free_device(_alloc)
        else:
            del self.ox_model
