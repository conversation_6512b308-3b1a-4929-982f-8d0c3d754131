"""
Base VOI extractor implementation.

This module provides a base implementation of VOI extraction
that can be extended for specific use cases.
"""

import math
from typing import Tuple, Sequence, Optional
import SimpleIT<PERSON> as sitk
import numpy as np
from crealife_volume import resample_image

from ..core.base_components import BaseVOIExtractor
from ..core.utils import validate_image, convert_to_numpy


class GenericVOIExtractor(BaseVOIExtractor):
    """Generic VOI extractor with common functionality."""
    
    def __init__(self, **kwargs):
        """
        Initialize generic VOI extractor.
        
        Args:
            **kwargs: Configuration parameters including:
                - target_spacing: Target spacing for resampling
                - norm_direction: Normalization direction
                - speed: Whether to use speed mode
        """
        super().__init__(**kwargs)
        self.target_spacing = kwargs.get('target_spacing', (1.0, 1.0, 1.0))
        self.norm_direction = kwargs.get('norm_direction', (1, 0, 0, 0, 1, 0, 0, 0, 1))
        self.speed_mode = kwargs.get('speed', False)
    
    def resample_image_to_standard(self, image: sitk.Image, 
                                  spacing: Optional[Tuple[float, ...]] = None) -> sitk.Image:
        """
        Resample image to standard spacing and direction.
        
        Args:
            image: Input SimpleITK image
            spacing: Target spacing (uses self.target_spacing if None)
            
        Returns:
            Resampled image
        """
        if not validate_image(image):
            raise ValueError("Invalid input image")
        
        if spacing is None:
            spacing = self.target_spacing
        
        return resample_image(
            image, 
            out_spacing=spacing, 
            out_direction=self.norm_direction
        )
    
    def extract_voi_by_bounds(self, image: sitk.Image, 
                             min_bounds: Tuple[int, ...], 
                             max_bounds: Tuple[int, ...]) -> sitk.Image:
        """
        Extract VOI by specifying bounds.
        
        Args:
            image: Input SimpleITK image
            min_bounds: Minimum bounds (x, y, z)
            max_bounds: Maximum bounds (x, y, z)
            
        Returns:
            Extracted VOI image
        """
        if not validate_image(image):
            raise ValueError("Invalid input image")
        
        # Ensure bounds are within image dimensions
        size = image.GetSize()
        min_bounds = tuple(max(0, min(b, s-1)) for b, s in zip(min_bounds, size))
        max_bounds = tuple(max(0, min(b, s-1)) for b, s in zip(max_bounds, size))
        
        # Calculate extraction parameters
        start_index = min_bounds
        extract_size = tuple(max_bounds[i] - min_bounds[i] + 1 for i in range(len(min_bounds)))
        
        # Extract region
        extractor = sitk.ExtractImageFilter()
        extractor.SetIndex(start_index)
        extractor.SetSize(extract_size)
        
        return extractor.Execute(image)
    
    def restore_from_voi(self, voi_result: sitk.Image, resampled_image: sitk.Image,
                        original_image: sitk.Image, roi_size: Tuple[int, ...],
                        indices: Tuple[int, ...]) -> sitk.Image:
        """
        Restore result from VOI to original image space.
        
        Args:
            voi_result: Result in VOI space
            resampled_image: Resampled full image
            original_image: Original input image
            roi_size: VOI size
            indices: VOI position indices
            
        Returns:
            Result restored to original image space
        """
        if not validate_image(voi_result) or not validate_image(resampled_image) or not validate_image(original_image):
            raise ValueError("Invalid input images")
        
        # Create result image in resampled space
        result_resampled = sitk.Image(resampled_image.GetSize(), sitk.sitkUInt8)
        result_resampled.CopyInformation(resampled_image)
        
        # Place VOI result in the correct position
        result_resampled[
            indices[0]:indices[0] + roi_size[0],
            indices[1]:indices[1] + roi_size[1], 
            indices[2]:indices[2] + roi_size[2]
        ] = voi_result
        
        # Resample back to original image space
        final_result = resample_image(
            result_resampled,
            out_size=original_image.GetSize(),
            out_spacing=original_image.GetSpacing(),
            out_direction=original_image.GetDirection(),
            out_origin=original_image.GetOrigin(),
            is_label=True
        )
        
        return final_result
    
    def extract_voi(self, image: sitk.Image, **kwargs) -> Tuple[sitk.Image, sitk.Image, Tuple[int, ...], Tuple[int, ...]]:
        """
        Extract VOI from input image. This is a generic implementation
        that should be overridden by specific extractors.
        
        Args:
            image: Input SimpleITK image
            **kwargs: Additional parameters
            
        Returns:
            Tuple containing:
            - voi_image: Extracted VOI image (full image in this generic case)
            - resampled_image: Resampled full image
            - indices: VOI position indices (0,0,0 in this generic case)
            - roi_size: VOI size (full image size in this generic case)
        """
        if not validate_image(image):
            raise ValueError("Invalid input image")
        
        # Generic implementation: use full image as VOI
        resampled_image = self.resample_image_to_standard(image)
        
        # In generic case, VOI is the full resampled image
        voi_image = resampled_image
        indices = (0, 0, 0)
        roi_size = voi_image.GetSize()
        
        return voi_image, resampled_image, indices, roi_size
    
    def validate_voi_result(self, voi_image: sitk.Image, roi_size: Tuple[int, ...]) -> bool:
        """
        Validate VOI extraction result.
        
        Args:
            voi_image: Extracted VOI image
            roi_size: VOI size
            
        Returns:
            True if VOI is valid
        """
        if not validate_image(voi_image):
            return False
        
        # Check if any dimension is zero
        if np.any(np.array(roi_size) == 0):
            return False
        
        # Check if VOI size matches image size
        actual_size = voi_image.GetSize()
        if actual_size != roi_size:
            return False
        
        return True
