"""
Base preprocessor implementation.

This module provides base preprocessing functionality that can be
extended for specific medical imaging applications.
"""

from typing import Union, Tuple, Optional, Dict, Any
import numpy as np
import SimpleIT<PERSON> as sitk
from scipy import ndimage

from ..core.base_components import BasePreprocessor
from ..core.utils import convert_to_numpy, clip_and_normalize


class GenericPreprocessor(BasePreprocessor):
    """Generic preprocessor with common medical image preprocessing operations."""
    
    def __init__(self, **kwargs):
        """
        Initialize generic preprocessor.
        
        Args:
            **kwargs: Configuration parameters including:
                - intensities: Dictionary with normalization parameters
                - clip_range: Intensity clipping range
                - target_spacing: Target spacing for resampling
                - interpolation_order: Interpolation order for resampling
        """
        super().__init__(**kwargs)
        
        # Intensity normalization parameters
        intensities = kwargs.get('intensities', {})
        self.intensity_mean = intensities.get('mean', 0.0)
        self.intensity_std = intensities.get('sd', 1.0)
        self.intensity_lower = intensities.get('lower', -1000.0)
        self.intensity_upper = intensities.get('upper', 1000.0)
        
        # Clipping range (alternative to intensities)
        self.clip_range = kwargs.get('clip_range', (self.intensity_lower, self.intensity_upper))
        
        # Resampling parameters
        self.target_spacing = kwargs.get('target_spacing', None)
        self.interpolation_order = kwargs.get('interpolation_order', 1)
        
        # Other preprocessing options
        self.normalize_intensity = kwargs.get('normalize_intensity', True)
        self.add_channel_dim = kwargs.get('add_channel_dim', True)
    
    def preprocess(self, image: Union[sitk.Image, np.ndarray], **kwargs) -> np.ndarray:
        """
        Preprocess image for model inference.
        
        Args:
            image: Input image (SimpleITK Image or numpy array)
            **kwargs: Additional parameters
            
        Returns:
            Preprocessed image as numpy array
        """
        # Convert to numpy array
        if isinstance(image, sitk.Image):
            image_array = convert_to_numpy(image)
        else:
            image_array = image.copy()
        
        # Apply preprocessing steps
        processed = self._apply_preprocessing_pipeline(image_array, **kwargs)
        
        return processed
    
    def _apply_preprocessing_pipeline(self, image_array: np.ndarray, **kwargs) -> np.ndarray:
        """
        Apply the complete preprocessing pipeline.
        
        Args:
            image_array: Input image array
            **kwargs: Additional parameters
            
        Returns:
            Preprocessed image array
        """
        # Step 1: Intensity clipping and normalization
        if self.normalize_intensity:
            processed = self.normalize_intensity_values(image_array)
        else:
            processed = image_array.astype(np.float32)
        
        # Step 2: Add channel dimension if needed
        if self.add_channel_dim and processed.ndim == 3:
            processed = processed[None]  # Add channel dimension
        
        return processed
    
    def normalize_intensity_values(self, image_array: np.ndarray) -> np.ndarray:
        """
        Normalize image intensity values.
        
        Args:
            image_array: Input image array
            
        Returns:
            Normalized image array
        """
        return clip_and_normalize(
            image_array,
            lower=self.intensity_lower,
            upper=self.intensity_upper,
            mean=self.intensity_mean,
            std=self.intensity_std
        )
    
    def resample_array(self, image_array: np.ndarray, 
                      current_spacing: Tuple[float, ...],
                      target_spacing: Optional[Tuple[float, ...]] = None) -> np.ndarray:
        """
        Resample image array to target spacing.
        
        Args:
            image_array: Input image array
            current_spacing: Current image spacing
            target_spacing: Target spacing (uses self.target_spacing if None)
            
        Returns:
            Resampled image array
        """
        if target_spacing is None:
            target_spacing = self.target_spacing
        
        if target_spacing is None or current_spacing == target_spacing:
            return image_array
        
        # Calculate zoom factors
        zoom_factors = tuple(
            current_spacing[i] / target_spacing[i] 
            for i in range(len(current_spacing))
        )
        
        # Resample using scipy
        resampled = ndimage.zoom(
            image_array, 
            zoom_factors, 
            order=self.interpolation_order,
            mode='constant',
            cval=self.intensity_lower
        )
        
        return resampled
    
    def apply_windowing(self, image_array: np.ndarray, 
                       window_center: float, window_width: float) -> np.ndarray:
        """
        Apply windowing to image.
        
        Args:
            image_array: Input image array
            window_center: Window center value
            window_width: Window width value
            
        Returns:
            Windowed image array
        """
        window_min = window_center - window_width / 2
        window_max = window_center + window_width / 2
        
        windowed = np.clip(image_array, window_min, window_max)
        
        # Normalize to [0, 1] range
        windowed = (windowed - window_min) / (window_max - window_min)
        
        return windowed.astype(np.float32)
    
    def apply_histogram_equalization(self, image_array: np.ndarray) -> np.ndarray:
        """
        Apply histogram equalization to enhance contrast.
        
        Args:
            image_array: Input image array
            
        Returns:
            Histogram equalized image array
        """
        # Convert to uint8 for histogram equalization
        image_uint8 = ((image_array - image_array.min()) / 
                      (image_array.max() - image_array.min()) * 255).astype(np.uint8)
        
        # Apply histogram equalization slice by slice for 3D images
        if image_uint8.ndim == 3:
            equalized = np.zeros_like(image_uint8)
            for i in range(image_uint8.shape[0]):
                # Use simple histogram equalization (can be replaced with more sophisticated methods)
                hist, bins = np.histogram(image_uint8[i].flatten(), 256, [0, 256])
                cdf = hist.cumsum()
                cdf_normalized = cdf * 255 / cdf[-1]
                equalized[i] = np.interp(image_uint8[i].flatten(), bins[:-1], cdf_normalized).reshape(image_uint8[i].shape)
        else:
            hist, bins = np.histogram(image_uint8.flatten(), 256, [0, 256])
            cdf = hist.cumsum()
            cdf_normalized = cdf * 255 / cdf[-1]
            equalized = np.interp(image_uint8.flatten(), bins[:-1], cdf_normalized).reshape(image_uint8.shape)
        
        # Convert back to float32
        return equalized.astype(np.float32) / 255.0
    
    def get_preprocessing_info(self) -> Dict[str, Any]:
        """
        Get information about preprocessing configuration.
        
        Returns:
            Dictionary containing preprocessing parameters
        """
        return {
            'intensity_mean': self.intensity_mean,
            'intensity_std': self.intensity_std,
            'intensity_lower': self.intensity_lower,
            'intensity_upper': self.intensity_upper,
            'clip_range': self.clip_range,
            'target_spacing': self.target_spacing,
            'interpolation_order': self.interpolation_order,
            'normalize_intensity': self.normalize_intensity,
            'add_channel_dim': self.add_channel_dim
        }
