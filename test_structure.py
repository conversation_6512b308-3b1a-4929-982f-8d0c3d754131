#!/usr/bin/env python3
"""
Simple structure test for the refactored lung lobe segmentation pipeline.

This script tests the basic structure and imports without requiring external dependencies.
"""

import os
import sys
import traceback

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_basic_imports():
    """Test basic Python imports without external dependencies."""
    print("=== Testing Basic Imports ===")
    
    try:
        # Test core modules
        from crealife_seglobes.core.base_components import (
            BaseVOIExtractor, BasePreprocessor, BaseModelInference, 
            BasePostprocessor, BasePipeline
        )
        print("✓ Core base components imported successfully")
        
        from crealife_seglobes.core.pipeline import GenericPipeline
        print("✓ Generic pipeline imported successfully")
        
        from crealife_seglobes.core.utils import validate_image_array
        print("✓ Core utils imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic imports failed: {e}")
        traceback.print_exc()
        return False


def test_component_imports():
    """Test component imports."""
    print("\n=== Testing Component Imports ===")
    
    try:
        # Test VOI components
        from crealife_seglobes.voi.lung_voi_extractor import LungVOIExtractor
        print("✓ Lung VOI extractor imported successfully")
        
        # Test preprocessing components
        from crealife_seglobes.preprocessing.lung_preprocessor import LungPreprocessor
        print("✓ Lung preprocessor imported successfully")
        
        # Test inference components
        from crealife_seglobes.inference.segmentation_inference import SegmentationModelInference
        print("✓ Segmentation inference imported successfully")
        
        # Test postprocessing components
        from crealife_seglobes.postprocessing.segmentation_postprocessor import SegmentationPostprocessor
        print("✓ Segmentation postprocessor imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Component imports failed: {e}")
        traceback.print_exc()
        return False


def test_application_imports():
    """Test application imports."""
    print("\n=== Testing Application Imports ===")
    
    try:
        from crealife_seglobes.applications.lung_lobe_segmentation import LungLobeSegmentationPipeline
        print("✓ Lung lobe segmentation pipeline imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Application imports failed: {e}")
        traceback.print_exc()
        return False


def test_config_imports():
    """Test configuration imports."""
    print("\n=== Testing Configuration Imports ===")
    
    try:
        from crealife_seglobes.config import (
            get_cfg_base_params,
            get_pipeline_config,
            get_lung_segmentation_config
        )
        print("✓ Configuration functions imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration imports failed: {e}")
        traceback.print_exc()
        return False


def test_class_instantiation():
    """Test basic class instantiation without external dependencies."""
    print("\n=== Testing Class Instantiation ===")
    
    try:
        # Test base components can be imported and have correct structure
        from crealife_seglobes.core.base_components import BaseVOIExtractor
        
        # Check that it's an abstract base class
        try:
            BaseVOIExtractor()
            print("✗ BaseVOIExtractor should be abstract")
            return False
        except TypeError:
            print("✓ BaseVOIExtractor is properly abstract")
        
        # Test concrete implementations can be instantiated
        from crealife_seglobes.postprocessing.base_postprocessor import GenericPostprocessor
        
        postprocessor = GenericPostprocessor()
        print("✓ GenericPostprocessor instantiated successfully")
        
        # Test that it has expected methods
        if hasattr(postprocessor, 'postprocess'):
            print("✓ GenericPostprocessor has postprocess method")
        else:
            print("✗ GenericPostprocessor missing postprocess method")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Class instantiation failed: {e}")
        traceback.print_exc()
        return False


def test_file_structure():
    """Test that all expected files exist."""
    print("\n=== Testing File Structure ===")
    
    expected_files = [
        'crealife_seglobes/__init__.py',
        'crealife_seglobes/api.py',
        'crealife_seglobes/config.py',
        'crealife_seglobes/core/__init__.py',
        'crealife_seglobes/core/base_components.py',
        'crealife_seglobes/core/pipeline.py',
        'crealife_seglobes/core/utils.py',
        'crealife_seglobes/voi/__init__.py',
        'crealife_seglobes/voi/lung_voi_extractor.py',
        'crealife_seglobes/voi/classification_based_extractor.py',
        'crealife_seglobes/preprocessing/__init__.py',
        'crealife_seglobes/preprocessing/base_preprocessor.py',
        'crealife_seglobes/preprocessing/medical_preprocessor.py',
        'crealife_seglobes/preprocessing/lung_preprocessor.py',
        'crealife_seglobes/inference/__init__.py',
        'crealife_seglobes/inference/base_inference.py',
        'crealife_seglobes/inference/onnx_inference.py',
        'crealife_seglobes/inference/tensorrt_inference.py',
        'crealife_seglobes/inference/segmentation_inference.py',
        'crealife_seglobes/postprocessing/__init__.py',
        'crealife_seglobes/postprocessing/base_postprocessor.py',
        'crealife_seglobes/postprocessing/connected_component_processor.py',
        'crealife_seglobes/postprocessing/morphological_processor.py',
        'crealife_seglobes/postprocessing/segmentation_postprocessor.py',
        'crealife_seglobes/applications/__init__.py',
        'crealife_seglobes/applications/lung_lobe_segmentation.py'
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in expected_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
        else:
            missing_files.append(file_path)
    
    print(f"✓ {len(existing_files)} files exist")
    if missing_files:
        print(f"✗ {len(missing_files)} files missing:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("✓ All expected files exist")
        return True


def test_syntax_validation():
    """Test that all Python files have valid syntax."""
    print("\n=== Testing Syntax Validation ===")
    
    python_files = []
    for root, dirs, files in os.walk('crealife_seglobes'):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    syntax_errors = []
    valid_files = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                compile(f.read(), file_path, 'exec')
            valid_files.append(file_path)
        except SyntaxError as e:
            syntax_errors.append((file_path, str(e)))
        except Exception as e:
            syntax_errors.append((file_path, f"Error reading file: {e}"))
    
    print(f"✓ {len(valid_files)} files have valid syntax")
    if syntax_errors:
        print(f"✗ {len(syntax_errors)} files have syntax errors:")
        for file_path, error in syntax_errors:
            print(f"  - {file_path}: {error}")
        return False
    else:
        print("✓ All Python files have valid syntax")
        return True


def main():
    """Run all structure tests."""
    print("=" * 60)
    print("REFACTORED PIPELINE STRUCTURE TEST")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Syntax Validation", test_syntax_validation),
        ("Basic Imports", test_basic_imports),
        ("Component Imports", test_component_imports),
        ("Application Imports", test_application_imports),
        ("Configuration Imports", test_config_imports),
        ("Class Instantiation", test_class_instantiation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("STRUCTURE TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "PASS" if success else "FAIL"
        print(f"{test_name:.<40} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All structure tests passed! The refactored pipeline structure is correct.")
        return 0
    else:
        print("⚠ Some structure tests failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
