import copy
import os
import sys

import numpy as np
from crealife_volume import CDBVolume

from crealife_seglobes.run import main

_base_dir = os.path.dirname(__file__)


if __name__ == '__main__':
    bk_argv = copy.copy(sys.argv)
    op_onnx = rf"{os.path.join(_base_dir, 'crealife_seglobes', 'demo_data', '404868_onnx.vol')}"
    op_trt = rf"{os.path.join(_base_dir, 'crealife_seglobes', 'demo_data', '404868_trt.vol')}"
    sys.argv += ['-i',
                 rf"{os.path.join(_base_dir, 'crealife_seglobes', 'demo_data', '404868.vol')}",
                 '-o',
                 op_onnx,
                 '-s', 'True',
                 '-ss', '0.95',
                 '--use_trt', 'False'
                 ]
    print(sys.argv)
    main()
    sys.argv = bk_argv
    sys.argv += ['-i',
                 rf"{os.path.join(_base_dir, 'crealife_seglobes', 'demo_data', '404868.vol')}",
                 '-o',
                 op_trt,
                 '-s', 'False',
                 '-ss', '0.95',
                 '--use_trt', 'True'
                 ]
    print(sys.argv)
    main()
    if os.path.exists(op_onnx) and os.path.exists(op_trt):
        vol_o = CDBVolume()
        vol_o.loadVolume(op_onnx)
        vol_t = CDBVolume()
        vol_t.loadVolume(op_trt)
        diff_num = np.sum(vol_o.getCTArr() != vol_t.getCTArr())
        print(diff_num)
