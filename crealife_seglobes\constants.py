import functools
import re

import pynvml

pynvml.nvmlInit()


@functools.lru_cache(maxsize=1)
def check_trt_env():
    try:
        import tensorrt as trt  # noqa: F401
        from pdutils.trt import trt_infer, trt_load, trt_prepare  # noqa: F401
        return True
    except ImportError:
        return False


DEVICE_NAME = pynvml.nvmlDeviceGetName(pynvml.nvmlDeviceGetHandleByIndex(0))
MODEL_TAG_MAP = {
    DEVICE_NAME: re.findall(r'\d+', DEVICE_NAME)[0]
}

CUR_DEVICE_TAG = MODEL_TAG_MAP.get(DEVICE_NAME, None)

HASLUNG_CLS_MN = 'haslung_cls.0.2.0'
LOBESEG_MN = 'lobe_seg.0.2.0'
ONNX_MODEL_INFO_MAP: dict = {
    HASLUNG_CLS_MN: {
        'input_shape': (32, 1, 128, 128),
        'use_fp16': False,
        'use_fp8': False,
    },
    LOBESEG_MN: {
        'input_shape': (1, 1, 128, 160, 160),
        'use_fp16': True,
        'use_fp8': False,
    }
}
