"""
Base components for medical image processing pipeline.

This module defines abstract base classes for each stage of the processing pipeline:
- VOI (Volume of Interest) extraction
- Preprocessing and resampling
- Model inference
- Postprocessing
- Overall pipeline coordination
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Tuple, Union
import SimpleITK as sitk
import numpy as np


class BaseVOIExtractor(ABC):
    """Base class for Volume of Interest (VOI) extraction."""
    
    def __init__(self, **kwargs):
        """Initialize VOI extractor with configuration parameters."""
        self.config = kwargs
    
    @abstractmethod
    def extract_voi(self, image: sitk.Image, **kwargs) -> <PERSON>ple[sitk.Image, sitk.Image, Tuple[int, ...], <PERSON><PERSON>[int, ...]]:
        """
        Extract volume of interest from input image.
        
        Args:
            image: Input SimpleITK image
            **kwargs: Additional parameters
            
        Returns:
            Tuple containing:
            - voi_image: Extracted VOI image
            - resampled_image: Resampled full image
            - indices: VOI position indices
            - roi_size: VOI size
        """
        pass
    
    @abstractmethod
    def restore_from_voi(self, voi_result: sitk.Image, resampled_image: sitk.Image, 
                        original_image: sitk.Image, roi_size: Tuple[int, ...], 
                        indices: Tuple[int, ...]) -> sitk.Image:
        """
        Restore result from VOI to original image space.
        
        Args:
            voi_result: Result in VOI space
            resampled_image: Resampled full image
            original_image: Original input image
            roi_size: VOI size
            indices: VOI position indices
            
        Returns:
            Result restored to original image space
        """
        pass


class BasePreprocessor(ABC):
    """Base class for image preprocessing."""
    
    def __init__(self, **kwargs):
        """Initialize preprocessor with configuration parameters."""
        self.config = kwargs
    
    @abstractmethod
    def preprocess(self, image: Union[sitk.Image, np.ndarray], **kwargs) -> np.ndarray:
        """
        Preprocess image for model inference.
        
        Args:
            image: Input image (SimpleITK Image or numpy array)
            **kwargs: Additional parameters
            
        Returns:
            Preprocessed image as numpy array
        """
        pass
    
    def normalize_intensity(self, image: np.ndarray, mean: float, std: float, 
                           lower: float, upper: float) -> np.ndarray:
        """
        Normalize image intensity.
        
        Args:
            image: Input image array
            mean: Target mean
            std: Target standard deviation
            lower: Lower clipping value
            upper: Upper clipping value
            
        Returns:
            Normalized image array
        """
        image = np.clip(image, lower, upper)
        image = (image - mean) / std
        return image.astype(np.float32)


class BaseModelInference(ABC):
    """Base class for model inference."""
    
    def __init__(self, **kwargs):
        """Initialize model inference with configuration parameters."""
        self.config = kwargs
        self.model = None
        self.use_trt = kwargs.get('use_trt', False)
    
    @abstractmethod
    def load_model(self, model_path: str, **kwargs) -> None:
        """
        Load inference model.
        
        Args:
            model_path: Path to model file
            **kwargs: Additional parameters
        """
        pass
    
    @abstractmethod
    def predict(self, image: np.ndarray, **kwargs) -> np.ndarray:
        """
        Perform model inference.
        
        Args:
            image: Input image array
            **kwargs: Additional parameters
            
        Returns:
            Model prediction result
        """
        pass
    
    @abstractmethod
    def postprocess_prediction(self, prediction: np.ndarray) -> np.ndarray:
        """
        Postprocess model prediction.
        
        Args:
            prediction: Raw model output
            
        Returns:
            Postprocessed prediction
        """
        pass


class BasePostprocessor(ABC):
    """Base class for postprocessing."""
    
    def __init__(self, **kwargs):
        """Initialize postprocessor with configuration parameters."""
        self.config = kwargs
    
    @abstractmethod
    def postprocess(self, mask: np.ndarray, **kwargs) -> np.ndarray:
        """
        Postprocess segmentation mask.
        
        Args:
            mask: Input segmentation mask
            **kwargs: Additional parameters
            
        Returns:
            Postprocessed mask
        """
        pass


class BasePipeline(ABC):
    """Base class for processing pipeline."""
    
    def __init__(self, voi_extractor: BaseVOIExtractor, 
                 preprocessor: BasePreprocessor,
                 model_inference: BaseModelInference,
                 postprocessor: BasePostprocessor,
                 **kwargs):
        """
        Initialize pipeline with components.
        
        Args:
            voi_extractor: VOI extraction component
            preprocessor: Preprocessing component
            model_inference: Model inference component
            postprocessor: Postprocessing component
            **kwargs: Additional configuration
        """
        self.voi_extractor = voi_extractor
        self.preprocessor = preprocessor
        self.model_inference = model_inference
        self.postprocessor = postprocessor
        self.config = kwargs
    
    @abstractmethod
    def process(self, image: sitk.Image, **kwargs) -> sitk.Image:
        """
        Process input image through the complete pipeline.
        
        Args:
            image: Input SimpleITK image
            **kwargs: Additional parameters
            
        Returns:
            Processed result image
        """
        pass
    
    def validate_components(self) -> bool:
        """
        Validate that all required components are properly initialized.
        
        Returns:
            True if all components are valid
        """
        components = [
            self.voi_extractor,
            self.preprocessor, 
            self.model_inference,
            self.postprocessor
        ]
        
        return all(comp is not None for comp in components)
