import argparse
import os.path
import unittest
from os.path import dirname

import numpy as np
import SimpleIT<PERSON> as sitk
from crealife_volume import CDBVolume

from crealife_seglobes.api import segment_from_file, segment_from_image
from crealife_seglobes.cls_lung import slice_lung_layers
from crealife_seglobes.utils import str2bool


class TestSegmentFromImage(unittest.TestCase):
    __BASE_DIR = dirname(dirname(__file__))
    IMG_P = os.path.join(__BASE_DIR, 'crealife_seglobes', 'demo_data', '404868.vol')
    IMG_O = os.path.join(__BASE_DIR, 'crealife_seglobes', 'demo_data', '404868_out.vol')

    def test_segment_from_image(self):
        """
        测试空图
        """
        img = sitk.Image((120, 160, 160), sitk.sitkInt16)
        img.SetSpacing((1, 1, 1))
        msk = segment_from_image(img)
        expected_msk_arr = np.zeros_like(sitk.GetArrayViewFromImage(img), dtype=np.uint8)
        self.assertIs(type(msk), sitk.Image)
        self.assertEqual(msk.GetSize(), img.GetSize())
        np.testing.assert_array_equal(sitk.GetArrayViewFromImage(msk), expected_msk_arr)

    # @patch('lung_voi.get_lung_slices')
    def test_segment_from_small_image(self):
        img = sitk.Image((20, 60, 60), sitk.sitkInt16)
        img.SetSpacing((1, 1, 1))
        msk = segment_from_image(img)
        self.assertIs(type(msk), sitk.Image)
        self.assertEqual(msk.GetSize(), img.GetSize())

    def test_segment_from_file(self):
        segment_from_file(self.IMG_P, self.IMG_O)

    def test_segment_from_file_speed(self):
        segment_from_file(self.IMG_P, self.IMG_O, {'speed': 'True'})

    def test_segment_from_file_use_onnx(self):
        segment_from_file(self.IMG_P, self.IMG_O, {'speed': 'True', 'use_trt': 'False'})

    def test_segment_from_large_image(self):
        vol = CDBVolume()
        vol.loadVolume(self.IMG_P)
        img = vol.getSitkImage()
        msk = segment_from_image(img)
        self.assertIs(type(msk), sitk.Image)
        self.assertEqual(msk.GetSize(), img.GetSize())


class TestStr2Bool(unittest.TestCase):
    # 测试正常路径
    def test_true_values(self):
        self.assertTrue(str2bool('yes'))
        self.assertTrue(str2bool('true'))
        self.assertTrue(str2bool('t'))
        self.assertTrue(str2bool('y'))
        self.assertTrue(str2bool('1'))

    def test_false_values(self):
        self.assertFalse(str2bool('no'))
        self.assertFalse(str2bool('false'))
        self.assertFalse(str2bool('f'))
        self.assertFalse(str2bool('n'))
        self.assertFalse(str2bool('0'))

    # 测试边缘情况
    def test_bool_input(self):
        self.assertTrue(str2bool(True))
        self.assertFalse(str2bool(False))

    def test_invalid_input(self):
        with self.assertRaises(argparse.ArgumentTypeError):
            str2bool('invalid')

    def test_case_insensitivity(self):
        self.assertTrue(str2bool('True'))
        self.assertTrue(str2bool('YeS'))
        self.assertFalse(str2bool('nO'))
        self.assertFalse(str2bool('FAlse'))


class TestSliceLungLayers(unittest.TestCase):
    def test_small_image(self):
        dummy_img = sitk.Image((512, 512, 5), sitk.sitkInt16)
        dummy_img.SetSpacing((0.4, 0.4, 1))
        voi_img, zmn, zmx = slice_lung_layers(dummy_img)
        self.assertEqual(zmn, 0)
        self.assertEqual(zmx, 0)
