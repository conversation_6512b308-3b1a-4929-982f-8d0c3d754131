"""
Medical image specific preprocessor.

This module provides preprocessing specifically designed for medical images,
with support for common medical imaging modalities and requirements.
"""

from typing import Union, Tuple, Optional, Dict, Any
import numpy as np
import SimpleITK as sitk

from .base_preprocessor import GenericPreprocessor
from ..core.utils import convert_to_numpy


class MedicalImagePreprocessor(GenericPreprocessor):
    """Preprocessor specifically designed for medical images."""
    
    def __init__(self, **kwargs):
        """
        Initialize medical image preprocessor.
        
        Args:
            **kwargs: Configuration parameters including:
                - modality: Imaging modality (CT, MRI, etc.)
                - body_region: Body region being imaged
                - hu_windowing: HU windowing parameters for CT
                - bias_correction: Whether to apply bias field correction
        """
        super().__init__(**kwargs)
        
        self.modality = kwargs.get('modality', 'CT').upper()
        self.body_region = kwargs.get('body_region', 'chest').lower()
        self.hu_windowing = kwargs.get('hu_windowing', None)
        self.bias_correction = kwargs.get('bias_correction', False)
        
        # Set modality-specific defaults
        self._set_modality_defaults()
    
    def _set_modality_defaults(self) -> None:
        """Set default parameters based on imaging modality."""
        if self.modality == 'CT':
            # CT-specific defaults
            if not hasattr(self, 'intensity_lower') or self.intensity_lower is None:
                self.intensity_lower = -1000.0  # Air HU value
            if not hasattr(self, 'intensity_upper') or self.intensity_upper is None:
                self.intensity_upper = 1000.0   # Bone HU value
            
            # Set body region specific windowing
            if self.body_region == 'chest' and self.hu_windowing is None:
                self.hu_windowing = {'center': -600, 'width': 1600}  # Lung window
            elif self.body_region == 'abdomen' and self.hu_windowing is None:
                self.hu_windowing = {'center': 40, 'width': 400}    # Soft tissue window
                
        elif self.modality == 'MRI':
            # MRI-specific defaults
            if not hasattr(self, 'intensity_lower') or self.intensity_lower is None:
                self.intensity_lower = 0.0
            if not hasattr(self, 'intensity_upper') or self.intensity_upper is None:
                self.intensity_upper = 4000.0
    
    def preprocess(self, image: Union[sitk.Image, np.ndarray], **kwargs) -> np.ndarray:
        """
        Preprocess medical image with modality-specific processing.
        
        Args:
            image: Input image (SimpleITK Image or numpy array)
            **kwargs: Additional parameters
            
        Returns:
            Preprocessed image as numpy array
        """
        # Convert to numpy array
        if isinstance(image, sitk.Image):
            image_array = convert_to_numpy(image)
            original_image = image
        else:
            image_array = image.copy()
            original_image = None
        
        # Apply medical image specific preprocessing
        processed = self._apply_medical_preprocessing(image_array, original_image, **kwargs)
        
        return processed
    
    def _apply_medical_preprocessing(self, image_array: np.ndarray, 
                                   original_image: Optional[sitk.Image] = None,
                                   **kwargs) -> np.ndarray:
        """
        Apply medical image specific preprocessing pipeline.
        
        Args:
            image_array: Input image array
            original_image: Original SimpleITK image (for metadata)
            **kwargs: Additional parameters
            
        Returns:
            Preprocessed image array
        """
        processed = image_array.copy()
        
        # Step 1: Modality-specific preprocessing
        if self.modality == 'CT':
            processed = self._preprocess_ct(processed, **kwargs)
        elif self.modality == 'MRI':
            processed = self._preprocess_mri(processed, original_image, **kwargs)
        
        # Step 2: Apply base preprocessing
        processed = self._apply_preprocessing_pipeline(processed, **kwargs)
        
        return processed
    
    def _preprocess_ct(self, image_array: np.ndarray, **kwargs) -> np.ndarray:
        """
        CT-specific preprocessing.
        
        Args:
            image_array: Input CT image array
            **kwargs: Additional parameters
            
        Returns:
            Preprocessed CT image array
        """
        processed = image_array.copy()
        
        # Apply HU windowing if specified
        if self.hu_windowing is not None:
            processed = self.apply_windowing(
                processed,
                window_center=self.hu_windowing['center'],
                window_width=self.hu_windowing['width']
            )
        else:
            # Standard CT intensity normalization
            processed = self.normalize_intensity_values(processed)
        
        return processed
    
    def _preprocess_mri(self, image_array: np.ndarray, 
                       original_image: Optional[sitk.Image] = None,
                       **kwargs) -> np.ndarray:
        """
        MRI-specific preprocessing.
        
        Args:
            image_array: Input MRI image array
            original_image: Original SimpleITK image
            **kwargs: Additional parameters
            
        Returns:
            Preprocessed MRI image array
        """
        processed = image_array.copy()
        
        # Apply bias field correction if enabled
        if self.bias_correction and original_image is not None:
            processed = self._apply_bias_correction(original_image)
            processed = convert_to_numpy(processed)
        
        # MRI intensity normalization (typically to [0, 1] range)
        processed = self._normalize_mri_intensity(processed)
        
        return processed
    
    def _apply_bias_correction(self, image: sitk.Image) -> sitk.Image:
        """
        Apply bias field correction to MRI image.
        
        Args:
            image: Input SimpleITK image
            
        Returns:
            Bias corrected image
        """
        try:
            # Use SimpleITK's N4 bias field correction
            corrector = sitk.N4BiasFieldCorrectionImageFilter()
            corrector.SetMaximumNumberOfIterations([4] * 4)
            
            # Convert to float for processing
            image_float = sitk.Cast(image, sitk.sitkFloat32)
            
            # Apply correction
            corrected = corrector.Execute(image_float)
            
            return corrected
            
        except Exception as e:
            print(f"Bias correction failed: {e}")
            return image
    
    def _normalize_mri_intensity(self, image_array: np.ndarray) -> np.ndarray:
        """
        Normalize MRI intensity values.
        
        Args:
            image_array: Input MRI image array
            
        Returns:
            Normalized MRI image array
        """
        # Remove background (assume background is close to 0)
        mask = image_array > np.percentile(image_array, 1)
        
        if np.any(mask):
            # Normalize based on non-background voxels
            foreground_values = image_array[mask]
            
            # Use percentile-based normalization to handle outliers
            p1, p99 = np.percentile(foreground_values, [1, 99])
            
            # Clip and normalize
            normalized = np.clip(image_array, p1, p99)
            normalized = (normalized - p1) / (p99 - p1)
        else:
            # Fallback: simple min-max normalization
            normalized = (image_array - image_array.min()) / (image_array.max() - image_array.min())
        
        return normalized.astype(np.float32)
    
    def get_modality_info(self) -> Dict[str, Any]:
        """
        Get modality-specific information.
        
        Returns:
            Dictionary containing modality information
        """
        info = self.get_preprocessing_info()
        info.update({
            'modality': self.modality,
            'body_region': self.body_region,
            'hu_windowing': self.hu_windowing,
            'bias_correction': self.bias_correction
        })
        return info
