"""
@target:
@author: <PERSON><PERSON><PERSON><PERSON>
@file: cls_lung.py
@time: 2024/02/19
@version:
@modify:
"""
import functools
import math
import os.path
from collections import Counter
from typing import Callable, Tuple

import cc3d
import numpy as np
import onnxruntime as ort
import SimpleITK as sitk
import tqdm
from scipy import ndimage

from .constants import (CUR_DEVICE_TAG, HASLUNG_CLS_MN,
                                         ONNX_MODEL_INFO_MAP, check_trt_env)


def batch_infer(ortssr_or_trteg: Callable,
                volarr: np.ndarray, resarr: np.ndarray, batch_sz: int = 16,
                is_trt: bool = False):
    tv = volarr[None, None]
    for si in tqdm.tqdm(range(math.ceil(tv.shape[2] / batch_sz)), disable=False):
        cur_b_size = batch_sz
        if (si + 1) * batch_sz > tv.shape[2]:
            cur_b_size = tv.shape[2] - si * batch_sz
            slice = tv[:, :, si * batch_sz:][0]
        else:
            slice = tv[:, :, si * batch_sz:(si + 1) * batch_sz][0]
        slice = np.transpose(slice, [1, 0, 2, 3]).astype(np.float32)
        if is_trt:
            outputs = ortssr_or_trteg([slice])[0][:cur_b_size]
        else:
            outputs = ortssr_or_trteg(
                ['output'],
                {'input': slice},
            )[0]
        predarr = np.argmax(outputs, 1)
        resarr[si * batch_sz:si * batch_sz + cur_b_size] = predarr
    return resarr


def slice_lung_layers(itkimg: sitk.Image, use_trt=True) -> Tuple[sitk.Image, int, int]:
    inimg_raw = sitk.GetArrayFromImage(itkimg)
    _base_dir = os.path.dirname(__file__)

    trt_path = os.path.join(_base_dir, 'resources', f'{HASLUNG_CLS_MN}.{CUR_DEVICE_TAG}.trt')

    if os.path.exists(trt_path) and check_trt_env() and use_trt:
        import tensorrt as trt
        from pdutils.trt import trt_infer, trt_load, trt_prepare
        trt_logger = trt.Logger()
        trt_engine = trt_load(trt_path, trt_logger)
        trt_context, inputs, outputs, allocations = \
            trt_prepare(ONNX_MODEL_INFO_MAP[HASLUNG_CLS_MN]['input_shape'], trt_engine)
        t_infer = functools.partial(trt_infer,
                                    trt_context=trt_context,
                                    inputs=inputs,
                                    outputs=outputs,
                                    allocations=allocations)
        use_trt = True
    else:
        use_trt = False
        providers = [('CUDAExecutionProvider', {
            'cudnn_conv_algo_search': 'DEFAULT',
            'device_id': 0,
            'arena_extend_strategy': 'kSameAsRequested',
            'do_copy_in_default_stream': False}), ]
        ort_cls_session = ort.InferenceSession(os.path.join(_base_dir, f'resources/{HASLUNG_CLS_MN}.onnx'),
                                               providers=providers)
        print(ort_cls_session.get_providers())
    # inimg_raw = np.clip(inimg_raw, -1000, 600)
    zoomedarr = ndimage.zoom(inimg_raw, (1., 128 / inimg_raw.shape[1], 128 / inimg_raw.shape[2]), order=1)
    zoomedarr = np.clip(zoomedarr, -1000, 600)
    clsres = np.zeros((zoomedarr.shape[0],), dtype=int)
    if use_trt:
        clsres = batch_infer(t_infer, zoomedarr, clsres, is_trt=True)
        del trt_engine
        del trt_context
        del inputs
        del outputs
        del allocations
    else:
        clsres = batch_infer(ort_cls_session.run, zoomedarr, clsres)
        del ort_cls_session
    clsres = ndimage.binary_closing(clsres,  iterations=10)
    regions = cc3d.connected_components(clsres)
    uq = np.unique(regions)
    kmp = Counter(regions)
    # 取kmp中key不等于0的value最大的key值
    if 0 in kmp:
        kmp.pop(0)
    if kmp:
        max_region = uq[np.argmax(list(map(lambda x: kmp[x], uq)))]
        for item in kmp:
            if kmp[item] > 20:
                regions[regions == item] = max_region
            else:
                regions[regions == item] = 0
        lung_layer_indexs = ndimage.binary_dilation(regions == max_region, iterations=2)
        idxs = np.where(lung_layer_indexs)

        if all(len(i) == 0 for i in idxs):
            print('not valid cls lung layer')
            zmn = 0
            zmx = itkimg.GetSize()[2]
        else:
            zmn = max(int(np.min(idxs)-100), 0)
            zmx = int(np.max(idxs))
        eif = sitk.ExtractImageFilter()
        eif.SetIndex((0, 0, int(zmn)))
        dimx, dimy, dimz = [int(it) for it in itkimg.GetSize()]
        eif.SetSize((dimx, dimy, int(zmx - zmn)))
        voi_img = eif.Execute(itkimg)
    else:
        # 没有肺区
        zmn = 0
        zmx = 0
        voi_img = itkimg
    return voi_img, zmn, zmx
