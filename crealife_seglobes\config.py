from os.path import abspath, dirname, join

from .constants import CUR_DEVICE_TAG, LOBESEG_MN


def get_cfg_base_params(**kwargs):
    _base_dir = dirname(abspath(__file__))
    pkg = join(_base_dir, 'resources')
    _kwargs = {
        'pkg': pkg,
        'input_tensor': {
            'name': 'input'
        },
        'output_tensor': {
            'name': 'output'
        }}
    if kwargs.get('speed'):
        _kwargs.update({
            'name': LOBESEG_MN,
            'intensities': {'lower': -999.0,
                            'mean': -799.9761962890625,
                            'sd': 180.14532470703125,
                            'upper': 78.0},
            'num_classes': 7,
            'patch_size': (128, 160, 160),
            'target_spacing': (2.0, 2.0, 2.0),
            'trt_path': join(pkg, f'{LOBESEG_MN}.{CUR_DEVICE_TAG}.trt')
        })
    else:
        _kwargs.update({
            'name': LOBESEG_MN,
            'intensities': {'lower': -999.0,
                            'mean': -799.9761962890625,
                            'sd': 180.14532470703125,
                            'upper': 78.0},
            'num_classes': 7,
            'patch_size': (128, 160, 160),
            'target_spacing': (1.0, 1.0, 1.0),
            'trt_path': join(pkg, f'{LOBESEG_MN}.{CUR_DEVICE_TAG}.trt')
        })
    return _kwargs
