from os.path import abspath, dirname, join
from typing import Dict, Any, Optional

from .constants import CUR_DEVICE_TAG, LOBESEG_MN


def get_cfg_base_params(**kwargs) -> Dict[str, Any]:
    """
    Get base configuration parameters for lung lobe segmentation.

    Args:
        **kwargs: Additional configuration parameters

    Returns:
        Dictionary containing base configuration parameters
    """
    _base_dir = dirname(abspath(__file__))
    pkg = join(_base_dir, 'resources')

    # Base configuration
    _kwargs = {
        'pkg': pkg,
        'input_tensor': {
            'name': 'input'
        },
        'output_tensor': {
            'name': 'output'
        },
        'name': LOBESEG_MN,
        'intensities': {
            'lower': -999.0,
            'mean': -799.9761962890625,
            'sd': 180.14532470703125,
            'upper': 78.0
        },
        'num_classes': 7,
        'patch_size': (128, 160, 160),
        'trt_path': join(pkg, f'{LOBESEG_MN}.{CUR_DEVICE_TAG}.trt'),
        'use_trt': True,
        'use_gpu': True,
        'batch_size': 1
    }

    # Speed-dependent configuration
    if kwargs.get('speed'):
        _kwargs.update({
            'target_spacing': (2.0, 2.0, 2.0),
            'step_size': 0.95
        })
    else:
        _kwargs.update({
            'target_spacing': (1.0, 1.0, 1.0),
            'step_size': 0.8
        })

    # Override with any provided kwargs
    _kwargs.update(kwargs)

    return _kwargs


def get_pipeline_config(**kwargs) -> Dict[str, Any]:
    """
    Get configuration for the new pipeline architecture.

    Args:
        **kwargs: Additional configuration parameters

    Returns:
        Dictionary containing pipeline configuration
    """
    base_config = get_cfg_base_params(**kwargs)

    # Pipeline-specific configuration
    pipeline_config = {
        # VOI extraction configuration
        'voi_config': {
            'use_classification': True,
            'classification_threshold': 0.5,
            'min_lung_size': 10000,
            'expand_ratio': 0.1
        },

        # Preprocessing configuration
        'preprocessing_config': {
            'normalize_intensity': True,
            'add_channel_dim': True,
            'modality': 'CT',
            'body_region': 'chest'
        },

        # Model inference configuration
        'inference_config': {
            'backend': 'auto',  # 'onnx', 'tensorrt', or 'auto'
            'use_patch_based': True,
            'use_gaussian_weighting': True
        },

        # Postprocessing configuration
        'postprocessing_config': {
            'use_connected_components': True,
            'use_morphological': True,
            'min_component_size': 1000,
            'fill_holes': True,
            'smooth_boundaries': False
        }
    }

    # Merge configurations
    merged_config = {**base_config, **pipeline_config}
    merged_config.update(kwargs)

    return merged_config


def get_lung_segmentation_config(**kwargs) -> Dict[str, Any]:
    """
    Get configuration specifically for lung lobe segmentation.

    Args:
        **kwargs: Additional configuration parameters

    Returns:
        Dictionary containing lung segmentation configuration
    """
    config = get_pipeline_config(**kwargs)

    # Lung-specific configuration
    lung_config = {
        'application': 'lung_lobe_segmentation',
        'expected_labels': {
            1: 'Right Upper Lobe',
            2: 'Right Middle Lobe',
            3: 'Right Lower Lobe',
            4: 'Left Upper Lobe',
            5: 'Left Lower Lobe'
        },
        'label_config': {
            1: {
                'operations': [
                    {'type': 'keep_largest_component'},
                    {'type': 'remove_small_components', 'min_size': 1000}
                ]
            },
            2: {
                'operations': [
                    {'type': 'keep_largest_component'},
                    {'type': 'remove_small_components', 'min_size': 1000}
                ]
            },
            3: {
                'operations': [
                    {'type': 'keep_largest_component'},
                    {'type': 'remove_small_components', 'min_size': 1000}
                ]
            },
            4: {
                'operations': [
                    {'type': 'keep_largest_component'},
                    {'type': 'remove_small_components', 'min_size': 1000}
                ]
            },
            5: {
                'operations': [
                    {'type': 'keep_largest_component'},
                    {'type': 'remove_small_components', 'min_size': 1000}
                ]
            }
        }
    }

    config.update(lung_config)
    return config


def create_custom_config(base_config: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
    """
    Create custom configuration by merging base config with custom parameters.

    Args:
        base_config: Base configuration dictionary
        **kwargs: Custom configuration parameters

    Returns:
        Merged configuration dictionary
    """
    if base_config is None:
        base_config = get_cfg_base_params()

    custom_config = base_config.copy()
    custom_config.update(kwargs)

    return custom_config


# Backward compatibility
def get_cfg_params(**kwargs) -> Dict[str, Any]:
    """Backward compatibility alias for get_cfg_base_params."""
    return get_cfg_base_params(**kwargs)
