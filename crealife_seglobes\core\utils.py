"""
Utility functions for the core pipeline components.

This module provides common utility functions used across
different pipeline components.
"""

import os
import functools
from typing import Union, Tuple, Optional, Any, Dict
import numpy as np
import SimpleITK as sitk


def validate_image(image: sitk.Image) -> bool:
    """
    Validate SimpleITK image.

    Args:
        image: SimpleITK image to validate

    Returns:
        True if image is valid
    """
    if image is None:
        return False

    try:
        size = image.GetSize()
        return all(s > 0 for s in size)
    except:
        return False


def validate_image_array(array: np.ndarray) -> bool:
    """
    Validate numpy image array.

    Args:
        array: Numpy array to validate

    Returns:
        True if array is valid
    """
    if array is None:
        return False

    if not isinstance(array, np.ndarray):
        return False

    if array.size == 0:
        return False

    if len(array.shape) < 2:
        return False

    return True


def convert_to_numpy(image: Union[sitk.Image, np.ndarray]) -> np.ndarray:
    """
    Convert image to numpy array.
    
    Args:
        image: Input image (SimpleITK Image or numpy array)
        
    Returns:
        Image as numpy array
    """
    if isinstance(image, sitk.Image):
        return sitk.GetArrayFromImage(image)
    elif isinstance(image, np.ndarray):
        return image
    else:
        raise TypeError(f"Unsupported image type: {type(image)}")


def convert_to_sitk(array: np.ndarray, reference_image: Optional[sitk.Image] = None) -> sitk.Image:
    """
    Convert numpy array to SimpleITK image.
    
    Args:
        array: Input numpy array
        reference_image: Reference image for copying metadata
        
    Returns:
        SimpleITK image
    """
    image = sitk.GetImageFromArray(array)
    
    if reference_image is not None:
        image.CopyInformation(reference_image)
    
    return image


def clip_and_normalize(image: np.ndarray, lower: float, upper: float, 
                      mean: float, std: float) -> np.ndarray:
    """
    Clip and normalize image intensity.
    
    Args:
        image: Input image array
        lower: Lower clipping value
        upper: Upper clipping value
        mean: Target mean for normalization
        std: Target standard deviation for normalization
        
    Returns:
        Normalized image array
    """
    # Clip values
    clipped = np.clip(image, lower, upper)
    
    # Normalize
    normalized = (clipped - mean) / std
    
    return normalized.astype(np.float32)


def ensure_directory_exists(path: str) -> None:
    """
    Ensure directory exists, create if necessary.
    
    Args:
        path: Directory path
    """
    os.makedirs(path, exist_ok=True)


def get_file_extension(filepath: str) -> str:
    """
    Get file extension from filepath.
    
    Args:
        filepath: Input file path
        
    Returns:
        File extension (including dot)
    """
    return os.path.splitext(filepath)[1].lower()


def is_medical_image_format(filepath: str) -> bool:
    """
    Check if file is a supported medical image format.
    
    Args:
        filepath: Input file path
        
    Returns:
        True if supported format
    """
    supported_extensions = {'.nii', '.nii.gz', '.mhd', '.mha', '.dcm', '.vol'}
    ext = get_file_extension(filepath)
    
    # Handle .nii.gz case
    if filepath.lower().endswith('.nii.gz'):
        return True
    
    return ext in supported_extensions


def safe_divide(numerator: Union[int, float, np.ndarray], 
                denominator: Union[int, float, np.ndarray],
                default_value: Union[int, float] = 0) -> Union[float, np.ndarray]:
    """
    Safely divide two numbers/arrays, handling division by zero.
    
    Args:
        numerator: Numerator value(s)
        denominator: Denominator value(s)
        default_value: Value to return when denominator is zero
        
    Returns:
        Division result or default value
    """
    if isinstance(denominator, np.ndarray):
        result = np.full_like(denominator, default_value, dtype=float)
        mask = denominator != 0
        result[mask] = numerator[mask] / denominator[mask]
        return result
    else:
        return numerator / denominator if denominator != 0 else default_value


@functools.lru_cache(maxsize=128)
def get_cached_config(config_key: str, default_value: Any = None) -> Any:
    """
    Get cached configuration value.
    
    Args:
        config_key: Configuration key
        default_value: Default value if key not found
        
    Returns:
        Configuration value
    """
    # This is a placeholder for configuration management
    # In practice, this would interface with a configuration system
    return default_value


def merge_configs(base_config: Dict[str, Any], 
                 override_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two configuration dictionaries.
    
    Args:
        base_config: Base configuration
        override_config: Override configuration
        
    Returns:
        Merged configuration
    """
    merged = base_config.copy()
    merged.update(override_config)
    return merged


def validate_config_keys(config: Dict[str, Any], 
                        required_keys: Tuple[str, ...]) -> bool:
    """
    Validate that configuration contains required keys.
    
    Args:
        config: Configuration dictionary
        required_keys: Required keys
        
    Returns:
        True if all required keys are present
    """
    return all(key in config for key in required_keys)
