import math
from typing import Sequence, <PERSON><PERSON>

import SimpleITK as sitk
from crealife_volume import resample_image

from .cls_lung import slice_lung_layers
from .config import get_cfg_base_params
from .utils import str2bool


def voi_restore(rsp_voimsk, rsp_img, sitkimg, roi_size, idxs):
    res_rspimg = sitk.Image(rsp_img.GetSize(), sitk.sitkUInt8)
    res_rspimg.CopyInformation(rsp_img)
    res_rspimg[idxs[0]:idxs[0] + roi_size[0],
               idxs[1]:idxs[1] + roi_size[1],
               idxs[2]: idxs[2] + roi_size[2]] = rsp_voimsk
    msk = resample_image(res_rspimg,
                         out_size=sitkimg.GetSize(),
                         out_spacing=sitkimg.GetSpacing(),
                         out_direction=sitkimg.GetDirection(),
                         out_origin=sitkimg.GetOrigin(),
                         is_label=True)
    return msk


def get_lung_slices(sitkimg: sitk.Image, **params_dict: dict) -> \
        Tuple[sitk.Image, sitk.Image, Sequence[int], Sequence[int]]:
    norm_direction = (1, 0, 0, 0, 1, 0, 0, 0, 1)
    _kwargs = get_cfg_base_params(**params_dict)
    tmp_rsp_img = resample_image(sitkimg, out_spacing=(1, 1, 1), out_direction=norm_direction)
    if params_dict.get('speed', False):
        rsp_img_in = resample_image(sitkimg, out_spacing=_kwargs.get('target_spacing'), out_direction=norm_direction)
    else:
        rsp_img_in = tmp_rsp_img
    voi_rspimg, zmn, zmx = slice_lung_layers(sitkimg, str2bool(params_dict.get('use_trt', True)))
    posd_min = sitkimg.TransformContinuousIndexToPhysicalPoint((0, 0, zmn))
    posd_max = sitkimg.TransformContinuousIndexToPhysicalPoint((0, 0, zmx))
    zmn = int(rsp_img_in.TransformPhysicalPointToContinuousIndex(posd_min)[2])
    zmx = math.ceil(rsp_img_in.TransformPhysicalPointToContinuousIndex(posd_max)[2])
    voi_rspimg = rsp_img_in[:, :, zmn:zmx]
    idxs = (0, 0, zmn)
    roi_size = [int(it) for it in voi_rspimg.GetSize()]
    return voi_rspimg, rsp_img_in, idxs, roi_size
