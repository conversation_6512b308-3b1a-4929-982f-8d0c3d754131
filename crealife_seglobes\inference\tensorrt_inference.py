"""
TensorRT model inference implementation.

This module provides TensorRT-based model inference functionality
for optimized GPU inference.
"""

import os
from typing import Dict, Any, List, Optional, Tuple
import numpy as np

from .base_inference import GenericModelInference
from ..constants import check_trt_env


class TensorRTModelInference(GenericModelInference):
    """TensorRT model inference for optimized GPU inference."""
    
    def __init__(self, **kwargs):
        """
        Initialize TensorRT model inference.
        
        Args:
            **kwargs: Configuration parameters including:
                - engine_path: Path to TensorRT engine file
                - max_batch_size: Maximum batch size
                - workspace_size: Workspace size for TensorRT
        """
        super().__init__(**kwargs)
        
        self.engine_path = kwargs.get('engine_path', kwargs.get('trt_path', ''))
        self.max_batch_size = kwargs.get('max_batch_size', 1)
        self.workspace_size = kwargs.get('workspace_size', 1 << 30)  # 1GB default
        
        # TensorRT components
        self.engine = None
        self.context = None
        self.inputs = None
        self.outputs = None
        self.allocations = None
        
        # Check TensorRT availability
        self.trt_available = check_trt_env()
        if not self.trt_available:
            raise ImportError("TensorRT not available. Please install TensorRT and pdutils.trt.")
        
        # Import TensorRT utilities
        try:
            from pdutils.trt import trt_infer, trt_load, trt_prepare
            self.trt_infer = trt_infer
            self.trt_load = trt_load
            self.trt_prepare = trt_prepare
        except ImportError:
            raise ImportError("pdutils.trt not available. Please install pdutils with TensorRT support.")
    
    def _load_model_implementation(self, **kwargs) -> None:
        """
        Load TensorRT model implementation.
        
        Args:
            **kwargs: Additional parameters
        """
        if not self.trt_available:
            raise RuntimeError("TensorRT not available")
        
        engine_path = kwargs.get('engine_path', self.engine_path)
        if not engine_path:
            engine_path = self.model_path
        
        if not os.path.exists(engine_path):
            raise FileNotFoundError(f"TensorRT engine file not found: {engine_path}")
        
        try:
            # Load TensorRT engine
            self.engine, self.context, self.inputs, self.outputs, self.allocations = self.trt_load(engine_path)
            
            print(f"TensorRT engine loaded successfully: {engine_path}")
            
        except Exception as e:
            raise RuntimeError(f"Failed to load TensorRT engine: {e}")
    
    def _predict_implementation(self, image: np.ndarray, **kwargs) -> np.ndarray:
        """
        TensorRT prediction implementation.
        
        Args:
            image: Input image array
            **kwargs: Additional parameters
            
        Returns:
            Model prediction result
        """
        if self.context is None:
            raise RuntimeError("TensorRT context not initialized")
        
        try:
            # Run TensorRT inference
            outputs = self.trt_infer(
                self.context, 
                self.inputs, 
                self.outputs, 
                self.allocations, 
                [image]
            )
            
            # Return first output
            prediction = outputs[0]
            
            return prediction
            
        except Exception as e:
            raise RuntimeError(f"TensorRT inference failed: {e}")
    
    def run_with_multiple_inputs(self, input_arrays: List[np.ndarray]) -> List[np.ndarray]:
        """
        Run inference with multiple inputs.
        
        Args:
            input_arrays: List of input arrays
            
        Returns:
            List of output arrays
        """
        if self.context is None:
            raise RuntimeError("TensorRT context not initialized")
        
        try:
            outputs = self.trt_infer(
                self.context,
                self.inputs,
                self.outputs,
                self.allocations,
                input_arrays
            )
            
            return outputs
            
        except Exception as e:
            raise RuntimeError(f"TensorRT inference failed: {e}")
    
    def get_engine_info(self) -> Dict[str, Any]:
        """
        Get TensorRT engine information.
        
        Returns:
            Dictionary containing engine information
        """
        if self.engine is None:
            return {}
        
        try:
            import tensorrt as trt
            
            info = {
                'max_batch_size': self.engine.max_batch_size,
                'num_bindings': self.engine.num_bindings,
                'has_implicit_batch_dimension': self.engine.has_implicit_batch_dimension,
            }
            
            # Get binding information
            bindings = []
            for i in range(self.engine.num_bindings):
                binding_info = {
                    'name': self.engine.get_binding_name(i),
                    'shape': self.engine.get_binding_shape(i),
                    'dtype': trt.nptype(self.engine.get_binding_dtype(i)),
                    'is_input': self.engine.binding_is_input(i)
                }
                bindings.append(binding_info)
            
            info['bindings'] = bindings
            
            return info
            
        except Exception as e:
            print(f"Failed to get engine info: {e}")
            return {}
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get TensorRT model information.
        
        Returns:
            Dictionary containing model information
        """
        info = super().get_model_info()
        info.update({
            'backend': 'TensorRT',
            'engine_path': self.engine_path,
            'max_batch_size': self.max_batch_size,
            'workspace_size': self.workspace_size,
            'engine_info': self.get_engine_info()
        })
        return info
    
    def cleanup(self) -> None:
        """Clean up TensorRT resources."""
        if self.engine is not None:
            del self.engine
            self.engine = None
        
        if self.context is not None:
            del self.context
            self.context = None
        
        if self.inputs is not None:
            del self.inputs
            self.inputs = None
        
        if self.outputs is not None:
            del self.outputs
            self.outputs = None
        
        if self.allocations is not None:
            del self.allocations
            self.allocations = None
        
        super().cleanup()


class TRTAdapter:
    """
    TensorRT adapter class to maintain compatibility with original code.
    
    This class provides the same interface as the original TRTAdapter.
    """
    
    def __init__(self):
        """Initialize TensorRT adapter."""
        self.trt_inference = None
        self.num_classes = None
        self.patch_size = None
    
    def load(self, engine_path: str, num_classes: int) -> None:
        """
        Load TensorRT engine.
        
        Args:
            engine_path: Path to TensorRT engine file
            num_classes: Number of output classes
        """
        self.num_classes = num_classes
        self.trt_inference = TensorRTModelInference(engine_path=engine_path)
        self.trt_inference.load_model(engine_path)
    
    def prepare(self, patch_size: Tuple[int, ...]) -> None:
        """
        Prepare for inference with given patch size.
        
        Args:
            patch_size: Patch size for inference
        """
        self.patch_size = patch_size
    
    def _predict(self, x: np.ndarray) -> np.ndarray:
        """
        Perform prediction using TensorRT.
        
        Args:
            x: Input array
            
        Returns:
            Prediction result
        """
        if self.trt_inference is None:
            raise RuntimeError("TensorRT inference not initialized")
        
        return self.trt_inference.predict(x)


# Convenience functions to maintain compatibility
def trt_infer(context, inputs, outputs, allocations, input_arrays):
    """Wrapper for TensorRT inference function."""
    try:
        from pdutils.trt import trt_infer as _trt_infer
        return _trt_infer(context, inputs, outputs, allocations, input_arrays)
    except ImportError:
        raise ImportError("pdutils.trt not available")


def trt_load(engine_path):
    """Wrapper for TensorRT engine loading function."""
    try:
        from pdutils.trt import trt_load as _trt_load
        return _trt_load(engine_path)
    except ImportError:
        raise ImportError("pdutils.trt not available")


def trt_prepare(engine, patch_size):
    """Wrapper for TensorRT preparation function."""
    try:
        from pdutils.trt import trt_prepare as _trt_prepare
        return _trt_prepare(engine, patch_size)
    except ImportError:
        raise ImportError("pdutils.trt not available")
