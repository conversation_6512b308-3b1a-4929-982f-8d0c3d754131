"""
Lung lobe segmentation pipeline implementation.

This module provides a complete lung lobe segmentation pipeline using the
generic architecture, maintaining compatibility with the original implementation.
"""

import os
from typing import Dict, Any, Optional, Union, Tuple
import numpy as np
import SimpleITK as sitk

from ..core.pipeline import GenericPipeline
from ..voi.lung_voi_extractor import LungVOIExtractor
from ..preprocessing.lung_preprocessor import LungPreprocessor
from ..inference.segmentation_inference import SegmentationModelInference
from ..postprocessing.segmentation_postprocessor import SegmentationPostprocessor
from ..config import get_cfg_base_params


class LungLobeSegmentationPipeline(GenericPipeline):
    """Complete lung lobe segmentation pipeline."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, **kwargs):
        """
        Initialize lung lobe segmentation pipeline.
        
        Args:
            config: Configuration dictionary
            **kwargs: Additional configuration parameters
        """
        # Merge configuration
        if config is None:
            config = {}
        merged_config = {**config, **kwargs}
        
        # Get base configuration
        base_config = get_cfg_base_params(**merged_config)
        
        # Initialize components
        voi_extractor = self._create_voi_extractor(base_config)
        preprocessor = self._create_preprocessor(base_config)
        model_inference = self._create_model_inference(base_config)
        postprocessor = self._create_postprocessor(base_config)
        
        # Initialize pipeline
        super().__init__(
            voi_extractor=voi_extractor,
            preprocessor=preprocessor,
            model_inference=model_inference,
            postprocessor=postprocessor,
            **merged_config
        )
        
        # Store configuration
        self.config = base_config
        self.lung_config = merged_config
    
    def _create_voi_extractor(self, config: Dict[str, Any]) -> LungVOIExtractor:
        """Create lung VOI extractor."""
        return LungVOIExtractor(**config)
    
    def _create_preprocessor(self, config: Dict[str, Any]) -> LungPreprocessor:
        """Create lung preprocessor."""
        return LungPreprocessor(**config)
    
    def _create_model_inference(self, config: Dict[str, Any]) -> SegmentationModelInference:
        """Create segmentation model inference."""
        model_inference = SegmentationModelInference(**config)
        
        # Load model if configuration is provided
        if 'pkg' in config and 'name' in config:
            model_path = os.path.join(config['pkg'], f"{config['name']}.onnx")
            trt_path = config.get('trt_path', '')
            
            if os.path.exists(model_path) or (trt_path and os.path.exists(trt_path)):
                model_inference.load_model(model_path, **config)
        
        return model_inference
    
    def _create_postprocessor(self, config: Dict[str, Any]) -> SegmentationPostprocessor:
        """Create segmentation postprocessor."""
        # Configure lung-specific postprocessing
        lung_postprocess_config = {
            'use_connected_components': True,
            'use_morphological': True,
            'label_config': {
                1: {'operations': [{'type': 'keep_largest_component'}, {'type': 'remove_small_components', 'min_size': 1000}]},
                2: {'operations': [{'type': 'keep_largest_component'}, {'type': 'remove_small_components', 'min_size': 1000}]},
                3: {'operations': [{'type': 'keep_largest_component'}, {'type': 'remove_small_components', 'min_size': 1000}]},
                4: {'operations': [{'type': 'keep_largest_component'}, {'type': 'remove_small_components', 'min_size': 1000}]},
                5: {'operations': [{'type': 'keep_largest_component'}, {'type': 'remove_small_components', 'min_size': 1000}]},
            }
        }
        
        merged_config = {**config, **lung_postprocess_config}
        return SegmentationPostprocessor(**merged_config)
    
    def process_image(self, image: Union[sitk.Image, np.ndarray], **kwargs) -> Dict[str, Any]:
        """
        Process lung CT image for lobe segmentation.
        
        Args:
            image: Input CT image
            **kwargs: Additional parameters
            
        Returns:
            Dictionary containing processing results
        """
        # Use the generic pipeline processing
        results = super().process_image(image, **kwargs)
        
        # Add lung-specific information
        results['application'] = 'lung_lobe_segmentation'
        results['num_lobes'] = len(np.unique(results['segmentation'])) - 1  # Exclude background
        
        return results
    
    def segment_lung_lobes(self, image: Union[sitk.Image, np.ndarray], **kwargs) -> np.ndarray:
        """
        Segment lung lobes from CT image.
        
        Args:
            image: Input CT image
            **kwargs: Additional parameters
            
        Returns:
            Lung lobe segmentation mask
        """
        results = self.process_image(image, **kwargs)
        return results['segmentation']
    
    def get_lobe_statistics(self, segmentation: np.ndarray) -> Dict[int, Dict[str, Any]]:
        """
        Get statistics for each lung lobe.
        
        Args:
            segmentation: Lung lobe segmentation mask
            
        Returns:
            Dictionary with statistics for each lobe
        """
        stats = {}
        
        for lobe_id in range(1, 6):  # Lung lobes 1-5
            if lobe_id in np.unique(segmentation):
                lobe_mask = (segmentation == lobe_id)
                stats[lobe_id] = {
                    'volume_voxels': np.sum(lobe_mask),
                    'centroid': self._calculate_centroid(lobe_mask),
                    'bounding_box': self._calculate_bounding_box(lobe_mask)
                }
            else:
                stats[lobe_id] = {
                    'volume_voxels': 0,
                    'centroid': None,
                    'bounding_box': None
                }
        
        return stats
    
    def _calculate_centroid(self, binary_mask: np.ndarray) -> Tuple[float, float, float]:
        """Calculate centroid of binary mask."""
        coords = np.where(binary_mask)
        if len(coords[0]) == 0:
            return (0.0, 0.0, 0.0)
        
        centroid = (
            float(np.mean(coords[0])),
            float(np.mean(coords[1])),
            float(np.mean(coords[2]))
        )
        return centroid
    
    def _calculate_bounding_box(self, binary_mask: np.ndarray) -> Optional[Tuple[Tuple[int, int], Tuple[int, int], Tuple[int, int]]]:
        """Calculate bounding box of binary mask."""
        coords = np.where(binary_mask)
        if len(coords[0]) == 0:
            return None
        
        bbox = (
            (int(np.min(coords[0])), int(np.max(coords[0]))),
            (int(np.min(coords[1])), int(np.max(coords[1]))),
            (int(np.min(coords[2])), int(np.max(coords[2])))
        )
        return bbox
    
    def validate_segmentation_quality(self, segmentation: np.ndarray) -> Dict[str, Any]:
        """
        Validate lung lobe segmentation quality.
        
        Args:
            segmentation: Lung lobe segmentation mask
            
        Returns:
            Validation results
        """
        validation = self.postprocessor.validate_segmentation(segmentation)
        
        # Add lung-specific validation
        expected_lobes = {1, 2, 3, 4, 5}  # Right upper, middle, lower; Left upper, lower
        present_lobes = set(np.unique(segmentation)) - {0}
        
        if len(present_lobes) < 3:
            validation['warnings'].append(f'Only {len(present_lobes)} lobes detected (expected 5)')
        
        missing_lobes = expected_lobes - present_lobes
        if missing_lobes:
            validation['warnings'].append(f'Missing lobes: {missing_lobes}')
        
        return validation
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """
        Get lung lobe segmentation pipeline information.
        
        Returns:
            Dictionary containing pipeline information
        """
        info = super().get_pipeline_info()
        info.update({
            'application': 'lung_lobe_segmentation',
            'expected_labels': {
                1: 'Right Upper Lobe',
                2: 'Right Middle Lobe', 
                3: 'Right Lower Lobe',
                4: 'Left Upper Lobe',
                5: 'Left Lower Lobe'
            },
            'config': self.config
        })
        return info


# Compatibility functions to maintain original API
def create_lung_segmentation_pipeline(**kwargs) -> LungLobeSegmentationPipeline:
    """
    Create lung lobe segmentation pipeline.
    
    Args:
        **kwargs: Configuration parameters
        
    Returns:
        Configured lung lobe segmentation pipeline
    """
    return LungLobeSegmentationPipeline(**kwargs)


def segment_lung_image(image: Union[sitk.Image, np.ndarray], 
                      config: Optional[Dict[str, Any]] = None, 
                      **kwargs) -> np.ndarray:
    """
    Segment lung lobes from CT image.
    
    This function provides a simple interface for lung lobe segmentation.
    
    Args:
        image: Input CT image
        config: Configuration dictionary
        **kwargs: Additional parameters
        
    Returns:
        Lung lobe segmentation mask
    """
    pipeline = LungLobeSegmentationPipeline(config, **kwargs)
    return pipeline.segment_lung_lobes(image, **kwargs)


def process_lung_ct_scan(image: Union[sitk.Image, np.ndarray],
                        config: Optional[Dict[str, Any]] = None,
                        **kwargs) -> Dict[str, Any]:
    """
    Complete processing of lung CT scan.
    
    Args:
        image: Input CT image
        config: Configuration dictionary
        **kwargs: Additional parameters
        
    Returns:
        Dictionary with complete processing results
    """
    pipeline = LungLobeSegmentationPipeline(config, **kwargs)
    results = pipeline.process_image(image, **kwargs)
    
    # Add lobe statistics
    if 'segmentation' in results:
        results['lobe_statistics'] = pipeline.get_lobe_statistics(results['segmentation'])
        results['quality_validation'] = pipeline.validate_segmentation_quality(results['segmentation'])
    
    return results
