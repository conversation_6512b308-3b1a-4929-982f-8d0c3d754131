import unittest

from tests.test_api import (TestSegmentFromImage, TestSliceLungLayers,
                            TestStr2Bool)
from tests.test_cc_analysis import TestKeepLargestCCEveryLabel
from tests.test_checkenv import TestCheckTrtEnv

api_suite = unittest.makeSuite(TestSegmentFromImage, 'test')
app_suite = unittest.makeSuite(TestKeepLargestCCEveryLabel, 'test')
utils_suite = unittest.makeSuite(TestStr2Bool, 'test')
slice_suite = unittest.makeSuite(TestSliceLungLayers, 'test')
env_suite = unittest.makeSuite(TestCheckTrtEnv, 'test')
suite = unittest.TestSuite((api_suite, app_suite, utils_suite, slice_suite))

if __name__ == '__main__':
    unittest.TextTestRunner().run(suite)
#     # coverage run -m unittest test_my_module.py
#     # coverage report
#     # coverage html
