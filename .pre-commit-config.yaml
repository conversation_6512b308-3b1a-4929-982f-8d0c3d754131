default_language_version:
  python: python3

exclude: 'scripts'

repos:
  - repo: https://gitee.com/mirrors_pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-added-large-files
      - id: check-yaml
      - id: double-quote-string-fixer
      - id: end-of-file-fixer
      - id: trailing-whitespace

  - repo: https://gitee.com/mirrors/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort (python)

  - repo: https://gitee.com/mirrors/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: ['flake8-bugbear']
        args: [--max-line-length, '120',
               --per-file-ignores, '*/__init__.py:F401']


  - repo: https://gitee.com/mirrors_pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        args: [--namespace-packages, --explicit-package-bases]
