__version__ = '0.3.0'

# Lazy imports to avoid loading heavy dependencies at import time
def _lazy_import_api():
    """Lazy import of API functions to avoid dependency issues."""
    from .api import (
        segment_from_file,
        segment_from_image,
        segment_from_sitk_image,
        segment_lung_from_file,
        segment_lung_lobes,
        process_lung_scan,
        create_lung_pipeline
    )
    return {
        'segment_from_file': segment_from_file,
        'segment_from_image': segment_from_image,
        'segment_from_sitk_image': segment_from_sitk_image,
        'segment_lung_from_file': segment_lung_from_file,
        'segment_lung_lobes': segment_lung_lobes,
        'process_lung_scan': process_lung_scan,
        'create_lung_pipeline': create_lung_pipeline
    }

# Create lazy-loaded API functions
_api_functions = None

def __getattr__(name):
    """Lazy loading of API functions."""
    global _api_functions
    if _api_functions is None:
        _api_functions = _lazy_import_api()

    if name in _api_functions:
        return _api_functions[name]

    # Handle other lazy imports
    if name == 'GenericPipeline':
        from .core.pipeline import GenericPipeline
        return GenericPipeline
    elif name == 'LungLobeSegmentationPipeline':
        from .applications.lung_lobe_segmentation import LungLobeSegmentationPipeline
        return LungLobeSegmentationPipeline
    elif name in ['get_cfg_base_params', 'get_pipeline_config', 'get_lung_segmentation_config', 'create_custom_config']:
        from .config import (
            get_cfg_base_params,
            get_pipeline_config,
            get_lung_segmentation_config,
            create_custom_config
        )
        config_funcs = {
            'get_cfg_base_params': get_cfg_base_params,
            'get_pipeline_config': get_pipeline_config,
            'get_lung_segmentation_config': get_lung_segmentation_config,
            'create_custom_config': create_custom_config
        }
        return config_funcs[name]

    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


# Define what should be available when using "from crealife_seglobes import *"
__all__ = [
    # Version
    '__version__',

    # Original API (backward compatibility)
    'segment_from_file',
    'segment_from_image',
    'segment_from_sitk_image',
    'segment_lung_from_file',

    # New pipeline API
    'segment_lung_lobes',
    'process_lung_scan',
    'create_lung_pipeline',

    # Pipeline classes
    'GenericPipeline',
    'LungLobeSegmentationPipeline',

    # Configuration functions
    'get_cfg_base_params',
    'get_pipeline_config',
    'get_lung_segmentation_config',
    'create_custom_config',

    # Base components for custom pipelines
    'BaseVOIExtractor',
    'BasePreprocessor',
    'BaseModelInference',
    'BasePostprocessor',
    'BasePipeline',

    # Specific implementations
    'LungVOIExtractor',
    'LungPreprocessor',
    'SegmentationModelInference',
    'SegmentationPostprocessor'
]
