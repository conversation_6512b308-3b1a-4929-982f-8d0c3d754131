"""
Classification-based VOI extractor.

This module provides VOI extraction based on classification models,
refactored from the original cls_lung.py implementation.
"""

import os
import math
from typing import Tuple, Callable, Optional
from collections import Counter
import numpy as np
import SimpleITK as sitk
import tqdm
from scipy import ndimage
import cc3d

from .base_voi_extractor import GenericVOIExtractor
from ..core.utils import validate_image, convert_to_numpy
from ..utils import str2bool


class ClassificationBasedVOIExtractor(GenericVOIExtractor):
    """VOI extractor using classification model to identify regions."""
    
    def __init__(self, **kwargs):
        """
        Initialize classification-based VOI extractor.
        
        Args:
            **kwargs: Configuration parameters including:
                - model_name: Name of classification model
                - model_path: Path to model file
                - use_trt: Whether to use TensorRT
                - batch_size: Batch size for inference
                - target_size: Target size for classification input
                - intensity_range: Intensity clipping range
        """
        super().__init__(**kwargs)
        self.model_name = kwargs.get('model_name', '')
        self.model_path = kwargs.get('model_path', '')
        self.use_trt = str2bool(kwargs.get('use_trt', True))
        self.batch_size = kwargs.get('batch_size', 16)
        self.target_size = kwargs.get('target_size', (128, 128))
        self.intensity_range = kwargs.get('intensity_range', (-1000, 600))
        
        # Model components (will be loaded when needed)
        self.model = None
        self.trt_engine = None
        self.trt_context = None
        
    def load_classification_model(self) -> None:
        """Load classification model (ONNX or TensorRT)."""
        if self.model is not None:
            return  # Already loaded
        
        if self.use_trt and self._check_trt_availability():
            self._load_trt_model()
        else:
            self._load_onnx_model()
    
    def _check_trt_availability(self) -> bool:
        """Check if TensorRT is available."""
        try:
            import tensorrt as trt
            from pdutils.trt import trt_infer, trt_load, trt_prepare
            return True
        except ImportError:
            return False
    
    def _load_trt_model(self) -> None:
        """Load TensorRT model."""
        try:
            from pdutils.trt import trt_infer, trt_load, trt_prepare
            
            trt_path = self.model_path.replace('.onnx', '.trt')
            if not os.path.exists(trt_path):
                raise FileNotFoundError(f"TensorRT model not found: {trt_path}")
            
            self.trt_engine, self.trt_context, inputs, outputs, allocations = trt_load(trt_path)
            self.model = lambda x: trt_infer(self.trt_context, inputs, outputs, allocations, [x])[0]
            
        except Exception as e:
            print(f"Failed to load TensorRT model: {e}")
            self._load_onnx_model()
    
    def _load_onnx_model(self) -> None:
        """Load ONNX model."""
        try:
            import onnxruntime as ort
            
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"ONNX model not found: {self.model_path}")
            
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
            session = ort.InferenceSession(self.model_path, providers=providers)
            
            self.model = lambda x: session.run(['output'], {'input': x})[0]
            self.use_trt = False
            
        except Exception as e:
            raise RuntimeError(f"Failed to load ONNX model: {e}")
    
    def batch_inference(self, volume_array: np.ndarray, result_array: np.ndarray) -> np.ndarray:
        """
        Perform batch inference on volume slices.
        
        Args:
            volume_array: Input volume array
            result_array: Output result array
            
        Returns:
            Classification results
        """
        if self.model is None:
            self.load_classification_model()
        
        tv = volume_array[None, None]  # Add batch and channel dimensions
        
        for si in tqdm.tqdm(range(math.ceil(tv.shape[2] / self.batch_size)), disable=False):
            cur_batch_size = self.batch_size
            
            # Handle last batch
            if (si + 1) * self.batch_size > tv.shape[2]:
                cur_batch_size = tv.shape[2] - si * self.batch_size
                slice_data = tv[:, :, si * self.batch_size:][0]
            else:
                slice_data = tv[:, :, si * self.batch_size:(si + 1) * self.batch_size][0]
            
            # Transpose for model input format
            slice_data = np.transpose(slice_data, [1, 0, 2, 3]).astype(np.float32)
            
            # Run inference
            if self.use_trt:
                outputs = self.model(slice_data)[:cur_batch_size]
            else:
                outputs = self.model(slice_data)
            
            # Get predictions
            predictions = np.argmax(outputs, 1)
            result_array[si * self.batch_size:si * self.batch_size + cur_batch_size] = predictions
        
        return result_array
    
    def extract_voi_by_classification(self, image: sitk.Image) -> Tuple[sitk.Image, int, int]:
        """
        Extract VOI using classification model.
        
        Args:
            image: Input SimpleITK image
            
        Returns:
            Tuple containing:
            - voi_image: Extracted VOI image
            - z_min: Minimum z index
            - z_max: Maximum z index
        """
        if not validate_image(image):
            raise ValueError("Invalid input image")
        
        # Convert to numpy array
        image_array = convert_to_numpy(image)
        
        # Resize for classification
        target_height, target_width = self.target_size
        zoomed_array = ndimage.zoom(
            image_array, 
            (1.0, target_height / image_array.shape[1], target_width / image_array.shape[2]), 
            order=1
        )
        
        # Clip intensity values
        zoomed_array = np.clip(zoomed_array, self.intensity_range[0], self.intensity_range[1])
        
        # Perform classification
        classification_result = np.zeros((zoomed_array.shape[0],), dtype=int)
        classification_result = self.batch_inference(zoomed_array, classification_result)
        
        # Post-process classification results
        classification_result = ndimage.binary_closing(classification_result, iterations=10)
        
        # Connected component analysis
        regions = cc3d.connected_components(classification_result)
        unique_regions = np.unique(regions)
        region_counts = Counter(regions)
        
        # Remove background (label 0)
        if 0 in region_counts:
            region_counts.pop(0)
        
        if region_counts:
            # Find largest region
            max_region = unique_regions[np.argmax([region_counts[r] for r in unique_regions if r != 0])]
            
            # Keep only significant regions
            for region_id in region_counts:
                if region_counts[region_id] > 20:
                    regions[regions == region_id] = max_region
                else:
                    regions[regions == region_id] = 0
            
            # Dilate the result
            lung_layer_indices = ndimage.binary_dilation(regions == max_region, iterations=2)
            indices = np.where(lung_layer_indices)
            
            if all(len(i) == 0 for i in indices):
                print('No valid classification result found')
                z_min = 0
                z_max = image.GetSize()[2]
            else:
                z_min = max(int(np.min(indices) - 100), 0)
                z_max = int(np.max(indices))
            
            # Extract VOI
            extractor = sitk.ExtractImageFilter()
            extractor.SetIndex((0, 0, int(z_min)))
            
            dim_x, dim_y, dim_z = [int(d) for d in image.GetSize()]
            extractor.SetSize((dim_x, dim_y, int(z_max - z_min)))
            
            voi_image = extractor.Execute(image)
        else:
            # No valid regions found
            z_min = 0
            z_max = 0
            voi_image = image
        
        return voi_image, z_min, z_max
    
    def extract_voi(self, image: sitk.Image, **kwargs) -> Tuple[sitk.Image, sitk.Image, Tuple[int, ...], Tuple[int, ...]]:
        """
        Extract VOI using classification-based approach.
        
        Args:
            image: Input SimpleITK image
            **kwargs: Additional parameters
            
        Returns:
            Tuple containing:
            - voi_image: Extracted VOI image
            - resampled_image: Resampled full image
            - indices: VOI position indices
            - roi_size: VOI size
        """
        if not validate_image(image):
            raise ValueError("Invalid input image")
        
        # First resample to standard spacing
        resampled_image = self.resample_image_to_standard(image)
        
        # Extract VOI using classification
        voi_full, z_min, z_max = self.extract_voi_by_classification(image)
        
        # Convert z coordinates to resampled space
        pos_min = image.TransformContinuousIndexToPhysicalPoint((0, 0, z_min))
        pos_max = image.TransformContinuousIndexToPhysicalPoint((0, 0, z_max))
        
        z_min_resampled = int(resampled_image.TransformPhysicalPointToContinuousIndex(pos_min)[2])
        z_max_resampled = math.ceil(resampled_image.TransformPhysicalPointToContinuousIndex(pos_max)[2])
        
        # Extract VOI from resampled image
        voi_image = resampled_image[:, :, z_min_resampled:z_max_resampled]
        
        indices = (0, 0, z_min_resampled)
        roi_size = tuple(int(s) for s in voi_image.GetSize())
        
        return voi_image, resampled_image, indices, roi_size
    
    def cleanup(self) -> None:
        """Clean up model resources."""
        if self.trt_engine is not None:
            del self.trt_engine
            self.trt_engine = None
        
        if self.trt_context is not None:
            del self.trt_context
            self.trt_context = None
        
        self.model = None
