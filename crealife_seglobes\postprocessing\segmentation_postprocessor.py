"""
Segmentation-specific postprocessor.

This module provides postprocessing functionality specifically designed for
medical image segmentation, integrating the original post_processing.py logic.
"""

from typing import Dict, Any, Optional, List, Tuple
import numpy as np
from functools import partial
from concurrent.futures import ThreadPoolExecutor

from .base_postprocessor import GenericPostprocessor
from .connected_component_processor import ConnectedComponentPostprocessor
from .morphological_processor import MorphologicalPostprocessor


class SegmentationPostprocessor(GenericPostprocessor):
    """Comprehensive postprocessor for medical image segmentation."""
    
    def __init__(self, **kwargs):
        """
        Initialize segmentation postprocessor.
        
        Args:
            **kwargs: Configuration parameters including:
                - label_config: Configuration for each label
                - use_connected_components: Whether to use connected component analysis
                - use_morphological: Whether to use morphological operations
                - parallel_processing: Whether to process labels in parallel
        """
        super().__init__(**kwargs)
        
        self.label_config = kwargs.get('label_config', {})
        self.use_connected_components = kwargs.get('use_connected_components', True)
        self.use_morphological = kwargs.get('use_morphological', False)
        self.parallel_processing = kwargs.get('parallel_processing', True)
        
        # Initialize specialized processors
        if self.use_connected_components:
            self.cc_processor = ConnectedComponentPostprocessor(**kwargs)
        
        if self.use_morphological:
            self.morph_processor = MorphologicalPostprocessor(**kwargs)
    
    def postprocess(self, mask: np.ndarray, **kwargs) -> np.ndarray:
        """
        Comprehensive postprocessing for segmentation mask.
        
        Args:
            mask: Input segmentation mask
            **kwargs: Additional parameters
            
        Returns:
            Postprocessed mask
        """
        if mask.size == 0:
            return mask
        
        processed = mask.copy()
        
        # Apply label-specific processing
        if self.label_config:
            processed = self.apply_label_specific_processing(processed)
        
        # Apply connected component analysis
        if self.use_connected_components and hasattr(self, 'cc_processor'):
            processed = self.cc_processor.postprocess(processed, **kwargs)
        
        # Apply morphological operations
        if self.use_morphological and hasattr(self, 'morph_processor'):
            processed = self.morph_processor.postprocess(processed, **kwargs)
        
        # Apply base postprocessing
        processed = super().postprocess(processed, **kwargs)
        
        return processed
    
    def apply_label_specific_processing(self, mask: np.ndarray) -> np.ndarray:
        """
        Apply label-specific postprocessing rules.
        
        Args:
            mask: Input mask
            
        Returns:
            Processed mask
        """
        processed = mask.copy()
        
        if self.parallel_processing:
            processed = self._process_labels_parallel(processed)
        else:
            processed = self._process_labels_sequential(processed)
        
        return processed
    
    def _process_labels_parallel(self, mask: np.ndarray) -> np.ndarray:
        """Process labels in parallel."""
        unique_labels = [label for label in np.unique(mask) if label > 0]
        
        if not unique_labels:
            return mask
        
        # Create partial function for processing
        process_func = partial(self._process_single_label, mask)
        
        # Process labels in parallel
        with ThreadPoolExecutor(max_workers=4) as executor:
            results = list(executor.map(process_func, unique_labels))
        
        # Combine results
        processed = np.zeros_like(mask)
        for label, label_mask in zip(unique_labels, results):
            processed[label_mask] = label
        
        return processed
    
    def _process_labels_sequential(self, mask: np.ndarray) -> np.ndarray:
        """Process labels sequentially."""
        processed = mask.copy()
        
        for label in np.unique(mask):
            if label == 0:  # Skip background
                continue
            
            if label in self.label_config:
                label_mask = self._process_single_label(mask, label)
                processed[mask == label] = 0
                processed[label_mask] = label
        
        return processed
    
    def _process_single_label(self, mask: np.ndarray, label: int) -> np.ndarray:
        """
        Process a single label according to its configuration.
        
        Args:
            mask: Input mask
            label: Label to process
            
        Returns:
            Boolean mask for the processed label
        """
        binary_mask = (mask == label)
        
        if label not in self.label_config:
            return binary_mask
        
        config = self.label_config[label]
        processed = binary_mask.copy()
        
        # Apply label-specific operations
        operations = config.get('operations', [])
        for operation in operations:
            processed = self._apply_label_operation(processed, operation)
        
        return processed
    
    def _apply_label_operation(self, binary_mask: np.ndarray, operation: Dict[str, Any]) -> np.ndarray:
        """
        Apply a single operation to a binary mask.
        
        Args:
            binary_mask: Input binary mask
            operation: Operation configuration
            
        Returns:
            Processed binary mask
        """
        op_type = operation.get('type', '')
        
        if op_type == 'remove_small_components':
            min_size = operation.get('min_size', 100)
            return self._remove_small_components_binary(binary_mask, min_size)
        
        elif op_type == 'keep_largest_component':
            return self._get_largest_component_binary(binary_mask)
        
        elif op_type == 'fill_holes':
            return self.fill_holes_in_mask(binary_mask)
        
        elif op_type == 'morphological':
            if hasattr(self, 'morph_processor'):
                morph_op = operation.get('operation', 'opening')
                return self.morph_processor.apply_single_operation(binary_mask, morph_op, **operation)
            else:
                return binary_mask
        
        else:
            print(f"Unknown label operation: {op_type}")
            return binary_mask
    
    def postprocess_lung_segmentation(self, mask: np.ndarray) -> np.ndarray:
        """
        Postprocess lung lobe segmentation specifically.
        
        This method implements the original post_processing.py logic.
        
        Args:
            mask: Input lung segmentation mask
            
        Returns:
            Postprocessed lung segmentation
        """
        if mask.size == 0:
            return mask
        
        processed = mask.copy()
        
        # Apply lung-specific postprocessing
        processed = self._apply_lung_postprocessing_pipeline(processed)
        
        return processed
    
    def _apply_lung_postprocessing_pipeline(self, mask: np.ndarray) -> np.ndarray:
        """
        Apply lung-specific postprocessing pipeline.
        
        Args:
            mask: Input mask
            
        Returns:
            Processed mask
        """
        processed = mask.copy()
        
        # Step 1: Keep largest connected component for each lobe
        if self.use_connected_components and hasattr(self, 'cc_processor'):
            processed = self.cc_processor.keep_largest_components_per_label(processed)
        
        # Step 2: Remove small components
        min_sizes = {
            1: 1000,  # Right upper lobe
            2: 1000,  # Right middle lobe  
            3: 1000,  # Right lower lobe
            4: 1000,  # Left upper lobe
            5: 1000,  # Left lower lobe
        }
        
        for label, min_size in min_sizes.items():
            if label in np.unique(processed):
                binary_mask = (processed == label)
                cleaned = self._remove_small_components_binary(binary_mask, min_size)
                processed[binary_mask & ~cleaned] = 0
        
        # Step 3: Fill small holes
        if self.use_morphological and hasattr(self, 'morph_processor'):
            for label in range(1, 6):  # Lung lobes 1-5
                if label in np.unique(processed):
                    binary_mask = (processed == label)
                    filled = self.morph_processor.remove_small_holes(binary_mask, hole_size=500)
                    processed[binary_mask] = 0
                    processed[filled] = label
        
        return processed
    
    def validate_segmentation(self, mask: np.ndarray) -> Dict[str, Any]:
        """
        Validate segmentation quality.
        
        Args:
            mask: Segmentation mask
            
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'statistics': {}
        }
        
        # Check for empty mask
        if mask.size == 0 or mask.max() == 0:
            validation_results['valid'] = False
            validation_results['errors'].append('Empty segmentation mask')
            return validation_results
        
        # Analyze each label
        unique_labels = np.unique(mask)
        for label in unique_labels:
            if label == 0:  # Skip background
                continue
            
            binary_mask = (mask == label)
            label_stats = self._analyze_label_quality(binary_mask, label)
            validation_results['statistics'][label] = label_stats
            
            # Check for quality issues
            if label_stats['num_components'] > 10:
                validation_results['warnings'].append(
                    f'Label {label} has many components ({label_stats["num_components"]})'
                )
            
            if label_stats['size'] < 100:
                validation_results['warnings'].append(
                    f'Label {label} is very small ({label_stats["size"]} voxels)'
                )
        
        return validation_results
    
    def _analyze_label_quality(self, binary_mask: np.ndarray, label: int) -> Dict[str, Any]:
        """
        Analyze quality metrics for a single label.
        
        Args:
            binary_mask: Binary mask for the label
            label: Label ID
            
        Returns:
            Dictionary with quality metrics
        """
        if hasattr(self, 'cc_processor'):
            analysis = self.cc_processor._analyze_binary_components(binary_mask)
        else:
            # Basic analysis
            analysis = {
                'num_components': 1 if np.any(binary_mask) else 0,
                'size': np.sum(binary_mask),
                'largest_size': np.sum(binary_mask)
            }
        
        return analysis
    
    def get_segmentation_info(self) -> Dict[str, Any]:
        """
        Get segmentation postprocessor information.
        
        Returns:
            Dictionary containing processor parameters
        """
        info = self.get_postprocessing_info()
        info.update({
            'processor_type': 'segmentation',
            'label_config': self.label_config,
            'use_connected_components': self.use_connected_components,
            'use_morphological': self.use_morphological,
            'parallel_processing': self.parallel_processing
        })
        return info


# Compatibility functions to maintain original API
def post_processing(mask: np.ndarray, **kwargs) -> np.ndarray:
    """
    Apply postprocessing to segmentation mask.
    
    This function maintains compatibility with the original implementation.
    
    Args:
        mask: Input segmentation mask
        **kwargs: Additional parameters
        
    Returns:
        Postprocessed mask
    """
    processor = SegmentationPostprocessor(**kwargs)
    return processor.postprocess_lung_segmentation(mask)


def validate_segmentation_result(mask: np.ndarray) -> Dict[str, Any]:
    """
    Validate segmentation result.
    
    Args:
        mask: Segmentation mask
        
    Returns:
        Validation results
    """
    processor = SegmentationPostprocessor()
    return processor.validate_segmentation(mask)
