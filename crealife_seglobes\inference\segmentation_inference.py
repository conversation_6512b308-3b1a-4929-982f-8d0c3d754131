"""
Segmentation model inference implementation.

This module provides segmentation-specific model inference functionality,
integrating the original seg_model.py logic with the new architecture.
"""

import os
from typing import Dict, Any, Tuple, Union, Optional
import numpy as np
from functools import partial
from itertools import product
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor
from queue import Queue
import threading

from .base_inference import GenericModelInference, PatchBasedInference
from .onnx_inference import ONNXModelInference, Model
from .tensorrt_inference import TensorRTModelInference, TRTAdapter
from ..constants import check_trt_env
from ..utils import str2bool
from ..preprocessing.lung_preprocessor import nmlsm


class SegmentationModelInference(GenericModelInference, PatchBasedInference):
    """Segmentation model inference with patch-based processing."""
    
    def __init__(self, **kwargs):
        """
        Initialize segmentation model inference.
        
        Args:
            **kwargs: Configuration parameters including:
                - intensities: Intensity normalization parameters
                - num_classes: Number of segmentation classes
                - patch_size: Patch size for inference
                - step_size: Step size for patch overlap
                - use_gaussian: Whether to use Gaussian weighting
        """
        # Initialize base classes
        GenericModelInference.__init__(self, **kwargs)
        
        # Get configuration
        self.config = kwargs
        self.intensities = kwargs.get('intensities', {})
        self.num_classes = kwargs.get('num_classes', 2)
        self.patch_size = kwargs.get('patch_size', (128, 160, 160))
        self.step_size = kwargs.get('step_size', 0.95)
        self.use_gaussian = kwargs.get('use_gaussian', True)
        
        # Initialize patch-based inference (remove patch_size and step_size from kwargs to avoid duplication)
        patch_kwargs = {k: v for k, v in kwargs.items() if k not in ['patch_size', 'step_size']}
        PatchBasedInference.__init__(self, self.patch_size, self.step_size, **patch_kwargs)
        
        # Model backend
        self.backend_model = None
        self.use_trt = str2bool(kwargs.get('use_trt', True))
        
        # Intensity normalization parameters
        if self.intensities:
            self.intensity_params = (
                self.intensities['mean'],
                self.intensities['sd'], 
                self.intensities['lower'],
                self.intensities['upper']
            )
        else:
            self.intensity_params = (0.0, 1.0, -1000.0, 1000.0)
    
    def load_model(self, model_path: str, **kwargs) -> None:
        """
        Load segmentation model.
        
        Args:
            model_path: Path to model file
            **kwargs: Additional parameters
        """
        self.model_path = model_path
        
        # Determine model type and load accordingly
        trt_path = kwargs.get('trt_path', '')
        
        if trt_path and os.path.exists(trt_path) and check_trt_env() and self.use_trt:
            self._load_tensorrt_model(trt_path, **kwargs)
        else:
            self._load_onnx_model(model_path, **kwargs)
        
        self.model_loaded = True
    
    def _load_tensorrt_model(self, trt_path: str, **kwargs) -> None:
        """Load TensorRT model."""
        self.backend_model = TRTAdapter()
        self.backend_model.load(trt_path, num_classes=self.num_classes)
        self.backend_model.prepare(self.patch_size)
        self.use_trt = True
        print(f'TensorRT model loaded. Step size: {self.step_size}')
    
    def _load_onnx_model(self, model_path: str, **kwargs) -> None:
        """Load ONNX model."""
        pkg = kwargs.get('pkg', '')
        
        # Use original Model class for compatibility
        if isinstance(self.config, list):
            self.backend_model = [Model(c, pkg) for c in self.config]
        else:
            self.backend_model = Model(self.config, pkg)
        
        self.use_trt = False
        print(f'ONNX model loaded. Step size: {self.step_size}')
    
    def _predict_implementation(self, image: np.ndarray, **kwargs) -> np.ndarray:
        """
        Segmentation prediction implementation.
        
        Args:
            image: Input image array
            **kwargs: Additional parameters
            
        Returns:
            Segmentation prediction result
        """
        if self.backend_model is None:
            raise RuntimeError("Model not loaded")
        
        # Preprocess image
        preprocessed = self.prep(image)
        
        # Perform patch-based prediction
        if self.use_trt:
            prediction = self._pred(preprocessed, bs=self.batch_size)
        else:
            with self.backend_model.gpu():
                prediction = self._pred(preprocessed, bs=self.batch_size)
        
        # Postprocess prediction
        segmentation = self.post(prediction)
        
        return segmentation
    
    def prep(self, img: np.ndarray) -> np.ndarray:
        """
        Preprocess image for segmentation.
        
        Args:
            img: Input image array
            
        Returns:
            Preprocessed image array
        """
        imgn = np.float32(img)
        imgn = nmlsm(imgn, *self.intensity_params)
        return imgn[None]  # Add batch dimension
    
    @staticmethod
    def post(prd: np.ndarray) -> np.ndarray:
        """
        Postprocess prediction.
        
        Args:
            prd: Raw prediction array
            
        Returns:
            Postprocessed segmentation
        """
        return prd.argmax(0).astype(np.uint8)
    
    def _predict_single(self, x: np.ndarray) -> np.ndarray:
        """
        Predict a single block/patch.
        
        Args:
            x: Input array
            
        Returns:
            Prediction result
        """
        if self.use_trt:
            return self.backend_model._predict(x)
        else:
            return self.backend_model.model.run(
                self.backend_model.t_ot, 
                {self.backend_model.t_in: x}
            )[0]
    
    def _pred(self, data: np.ndarray, bs: int = 1) -> np.ndarray:
        """
        Perform patch-based prediction using original high-performance implementation.

        Args:
            data: Input data array
            bs: Batch size

        Returns:
            Aggregated prediction result
        """
        # Use the original high-performance implementation
        from ..seg_model import pad_nd_image, compute_steps_for_sliding_window, get_gaussian
        import queue
        from functools import partial
        from itertools import product
        import threading

        print(data.shape)
        data_padded, slicer = pad_nd_image(data, self.patch_size, 'constant',
                                          None, True, None)
        steps = compute_steps_for_sliding_window(self.patch_size, data_padded.shape[1:], self.step_size)
        print(data_padded.shape)
        num_tiles = len(steps[0]) * len(steps[1]) * len(steps[2])
        print(steps)
        print('num_tiles:', num_tiles)
        gaussian = get_gaussian(self.patch_size) if self.use_gaussian and num_tiles > 1 else None
        predicted_logits = np.zeros((self.num_classes,) + data_padded.shape[1:], dtype=np.float32)
        n_predictions = np.zeros((1,) + data_padded.shape[1:], dtype=np.float32)

        q = queue.Queue()

        def infer_block(sl, data, q):
            try:
                x = data[sl]
                prediction = self._predict_single(x)
                q.put((sl, prediction))
            except Exception as e:
                print(f"Error in infer_block: {e}")
                q.put((sl, None))

        def writer_thread_func():
            while True:
                item = q.get()
                if item is None:
                    q.task_done()
                    break
                sl, prediction = item
                if prediction is not None:
                    if gaussian is not None:
                        prediction = prediction * gaussian
                    predicted_logits[sl] += prediction[0]
                    n_predictions[sl[0:1]] += gaussian if gaussian is not None else 1
                q.task_done()

        writer_thread = threading.Thread(target=writer_thread_func)
        writer_thread.start()

        _infer_func = partial(infer_block, data=data_padded, q=q)

        # 计算所有可能的切片
        for s in product(*steps):
            sl = (slice(None),) + tuple(slice(s[i], s[i] + self.patch_size[i]) for i in range(3))
            _infer_func(sl)
        q.join()
        q.put(None)
        writer_thread.join()

        slicer = tuple([slice(None), *slicer[1:]])
        predicted_logits = predicted_logits[slicer]
        n_predictions = n_predictions[slicer]
        predicted_logits /= n_predictions
        return predicted_logits
    
    def _sliding_window_prediction(self, data: np.ndarray, bs: int) -> np.ndarray:
        """
        Sliding window prediction implementation.
        
        Args:
            data: Input data array
            bs: Batch size
            
        Returns:
            Prediction result
        """
        # Get data shape (assuming format: [batch, channel, depth, height, width])
        if data.ndim == 4:
            data = data[None]  # Add batch dimension
        
        _, _, d, h, w = data.shape
        pd, ph, pw = self.patch_size
        
        # Calculate step sizes
        step_d = max(1, int(pd * self.step_size))
        step_h = max(1, int(ph * self.step_size))
        step_w = max(1, int(pw * self.step_size))
        
        # Initialize output arrays
        predicted_logits = np.zeros((self.num_classes, d, h, w), dtype=np.float32)
        n_predictions = np.zeros((d, h, w), dtype=np.float32)
        
        # Generate patch coordinates
        for z in range(0, d - pd + 1, step_d):
            for y in range(0, h - ph + 1, step_h):
                for x in range(0, w - pw + 1, step_w):
                    # Extract patch
                    patch = data[:, :, z:z+pd, y:y+ph, x:x+pw]
                    
                    # Predict patch
                    patch_pred = self._predict_single(patch)
                    
                    # Accumulate predictions
                    predicted_logits[:, z:z+pd, y:y+ph, x:x+pw] += patch_pred[0]
                    n_predictions[z:z+pd, y:y+ph, x:x+pw] += 1
        
        # Average overlapping predictions
        n_predictions[n_predictions == 0] = 1  # Avoid division by zero
        predicted_logits /= n_predictions[None]
        
        return predicted_logits
    
    def postprocess_prediction(self, prediction: np.ndarray) -> np.ndarray:
        """
        Postprocess segmentation prediction.
        
        Args:
            prediction: Raw model output
            
        Returns:
            Postprocessed segmentation
        """
        return self.post(prediction)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get segmentation model information.
        
        Returns:
            Dictionary containing model information
        """
        info = super().get_model_info()
        info.update({
            'model_type': 'segmentation',
            'num_classes': self.num_classes,
            'patch_size': self.patch_size,
            'step_size': self.step_size,
            'use_gaussian': self.use_gaussian,
            'intensity_params': self.intensity_params,
            'backend': 'TensorRT' if self.use_trt else 'ONNX'
        })
        return info
    
    def cleanup(self) -> None:
        """Clean up model resources."""
        if self.backend_model is not None:
            if hasattr(self.backend_model, 'cleanup'):
                self.backend_model.cleanup()
            del self.backend_model
            self.backend_model = None
        
        super().cleanup()


# Compatibility classes
class SegModel(SegmentationModelInference, TRTAdapter):
    """
    Segmentation model class maintaining compatibility with original code.
    
    This class combines the new architecture with the original SegModel interface.
    """
    
    def __init__(self, **kwargs):
        """Initialize SegModel with original interface."""
        SegmentationModelInference.__init__(self, **kwargs)
        TRTAdapter.__init__(self)
        
        # Load model automatically if configuration is provided
        if self.config:
            self._auto_load_model()
    
    def _auto_load_model(self) -> None:
        """Automatically load model based on configuration."""
        trt_path = self.config.get('trt_path', '')
        pkg = self.config.get('pkg', '')
        name = self.config.get('name', '')
        
        if trt_path:
            model_path = trt_path
        else:
            model_path = os.path.join(pkg, f'{name}.onnx')
        
        self.load_model(model_path, **self.config)
    
    def __call__(self, img: np.ndarray) -> np.ndarray:
        """
        Process image through segmentation model.
        
        Args:
            img: Input image array
            
        Returns:
            Segmentation result
        """
        return self.predict(img)
