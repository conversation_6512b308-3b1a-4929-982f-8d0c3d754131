"""
Connected component postprocessor.

This module provides connected component analysis and processing functionality,
refactored from the original cc_analysis.py implementation.
"""

import functools
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, Optional, List
import numpy as np

from .base_postprocessor import GenericPostprocessor


class ConnectedComponentPostprocessor(GenericPostprocessor):
    """Postprocessor specialized for connected component analysis."""
    
    def __init__(self, **kwargs):
        """
        Initialize connected component postprocessor.
        
        Args:
            **kwargs: Configuration parameters including:
                - keep_largest_per_label: Whether to keep only largest component per label
                - min_component_size_per_label: Dict of minimum sizes per label
                - connectivity: Connectivity for component analysis (6 or 26 for 3D)
                - parallel_processing: Whether to use parallel processing
                - num_workers: Number of worker threads
        """
        super().__init__(**kwargs)
        
        self.keep_largest_per_label = kwargs.get('keep_largest_per_label', True)
        self.min_component_size_per_label = kwargs.get('min_component_size_per_label', {})
        self.connectivity = kwargs.get('connectivity', 6)
        self.parallel_processing = kwargs.get('parallel_processing', True)
        self.num_workers = kwargs.get('num_workers', 4)
    
    def postprocess(self, mask: np.ndarray, **kwargs) -> np.ndarray:
        """
        Postprocess mask using connected component analysis.
        
        Args:
            mask: Input segmentation mask
            **kwargs: Additional parameters
            
        Returns:
            Postprocessed mask
        """
        if mask.size == 0:
            return mask
        
        processed = mask.copy()
        
        # Apply connected component processing
        if self.keep_largest_per_label:
            processed = self.keep_largest_components_per_label(processed)
        
        # Apply base postprocessing
        processed = super().postprocess(processed, **kwargs)
        
        return processed
    
    def keep_largest_components_per_label(self, mask: np.ndarray) -> np.ndarray:
        """
        Keep only the largest connected component for each label.
        
        Args:
            mask: Input mask
            
        Returns:
            Mask with only largest components per label
        """
        if mask.size == 0:
            return mask
        
        label_max = np.max(mask)
        if label_max == 0:
            return mask
        
        # Process each label
        if self.parallel_processing:
            processed = self._process_labels_parallel(mask, label_max)
        else:
            processed = self._process_labels_sequential(mask, label_max)
        
        return processed
    
    def _process_labels_parallel(self, mask: np.ndarray, label_max: int) -> np.ndarray:
        """
        Process labels in parallel.
        
        Args:
            mask: Input mask
            label_max: Maximum label value
            
        Returns:
            Processed mask
        """
        # Create partial function for processing
        process_func = functools.partial(self._remove_non_largest_components, mask)
        
        # Process labels in parallel
        label_ids = list(range(1, label_max + 1))
        
        with ThreadPoolExecutor(self.num_workers) as executor:
            executor.map(process_func, label_ids)
        
        return mask
    
    def _process_labels_sequential(self, mask: np.ndarray, label_max: int) -> np.ndarray:
        """
        Process labels sequentially.
        
        Args:
            mask: Input mask
            label_max: Maximum label value
            
        Returns:
            Processed mask
        """
        for label_id in range(1, label_max + 1):
            self._remove_non_largest_components(mask, label_id)
        
        return mask
    
    def _remove_non_largest_components(self, mask: np.ndarray, label_id: int) -> None:
        """
        Remove non-largest components for a specific label (in-place).
        
        Args:
            mask: Input/output mask (modified in-place)
            label_id: Label ID to process
        """
        # Get binary mask for this label
        binary_mask = (mask == label_id)
        
        if not np.any(binary_mask):
            return
        
        # Get largest component
        largest_component = self._get_largest_component_binary(binary_mask)
        
        # Update mask in-place
        mask[binary_mask & ~largest_component] = 0
    
    def keep_largest_connected_component(self, mask: np.ndarray, label: int) -> np.ndarray:
        """
        Keep largest connected component for a specific label.
        
        This function maintains compatibility with the original implementation.
        
        Args:
            mask: Input mask
            label: Label to process
            
        Returns:
            Boolean mask of largest component
        """
        binary_mask = (mask == label)
        return self._get_largest_component_binary(binary_mask)
    
    def analyze_components(self, mask: np.ndarray, label: Optional[int] = None) -> Dict[str, Any]:
        """
        Analyze connected components in mask.
        
        Args:
            mask: Input mask
            label: Specific label to analyze (None for all labels)
            
        Returns:
            Dictionary with component analysis results
        """
        results = {}
        
        if label is not None:
            # Analyze specific label
            binary_mask = (mask == label)
            results[label] = self._analyze_binary_components(binary_mask)
        else:
            # Analyze all labels
            unique_labels = np.unique(mask)
            for lbl in unique_labels:
                if lbl == 0:  # Skip background
                    continue
                binary_mask = (mask == lbl)
                results[lbl] = self._analyze_binary_components(binary_mask)
        
        return results
    
    def _analyze_binary_components(self, binary_mask: np.ndarray) -> Dict[str, Any]:
        """
        Analyze components in binary mask.
        
        Args:
            binary_mask: Binary input mask
            
        Returns:
            Dictionary with analysis results
        """
        try:
            import cc3d
            import skimage.measure
            
            # Connected component analysis
            labeled = cc3d.connected_components(binary_mask, connectivity=self.connectivity)
            unique_labels, counts = np.unique(labeled, return_counts=True)
            
            # Remove background
            non_bg_mask = unique_labels != 0
            component_labels = unique_labels[non_bg_mask]
            component_sizes = counts[non_bg_mask]
            
            # Get region properties
            regions = skimage.measure.regionprops(labeled)
            
            # Sort by area (largest first)
            regions.sort(key=lambda r: r.area, reverse=True)
            
            return {
                'num_components': len(component_labels),
                'component_sizes': component_sizes.tolist(),
                'largest_size': component_sizes.max() if len(component_sizes) > 0 else 0,
                'total_size': component_sizes.sum() if len(component_sizes) > 0 else 0,
                'regions': regions
            }
            
        except ImportError:
            # Fallback analysis without detailed region properties
            from scipy import ndimage
            
            labeled, num_features = ndimage.label(binary_mask)
            component_sizes = ndimage.sum(binary_mask, labeled, range(1, num_features + 1))
            
            return {
                'num_components': num_features,
                'component_sizes': component_sizes.tolist(),
                'largest_size': component_sizes.max() if num_features > 0 else 0,
                'total_size': component_sizes.sum() if num_features > 0 else 0,
                'regions': []
            }
    
    def filter_components_by_size(self, mask: np.ndarray, 
                                 min_sizes: Optional[Dict[int, int]] = None) -> np.ndarray:
        """
        Filter components by size for each label.
        
        Args:
            mask: Input mask
            min_sizes: Dictionary mapping label to minimum size
            
        Returns:
            Filtered mask
        """
        if min_sizes is None:
            min_sizes = self.min_component_size_per_label
        
        if not min_sizes:
            return mask
        
        processed = mask.copy()
        
        for label, min_size in min_sizes.items():
            if label in np.unique(mask):
                binary_mask = (mask == label)
                filtered = self._remove_small_components_binary(binary_mask, min_size)
                processed[binary_mask & ~filtered] = 0
        
        return processed
    
    def get_component_info(self) -> Dict[str, Any]:
        """
        Get connected component processor information.
        
        Returns:
            Dictionary containing processor parameters
        """
        info = self.get_postprocessing_info()
        info.update({
            'processor_type': 'connected_component',
            'keep_largest_per_label': self.keep_largest_per_label,
            'min_component_size_per_label': self.min_component_size_per_label,
            'connectivity': self.connectivity,
            'parallel_processing': self.parallel_processing,
            'num_workers': self.num_workers
        })
        return info


# Compatibility functions to maintain original API
def keep_largest_connected_component(mask: np.ndarray, label: int) -> np.ndarray:
    """
    Keep largest connected component for a specific label.
    
    This function maintains compatibility with the original implementation.
    
    Args:
        mask: Input mask
        label: Label to process
        
    Returns:
        Boolean mask of largest component
    """
    processor = ConnectedComponentPostprocessor()
    return processor.keep_largest_connected_component(mask, label)


def _keep_largest_cc_every_label(mask: np.ndarray) -> np.ndarray:
    """
    Keep largest connected component for every label.
    
    This function maintains compatibility with the original implementation.
    
    Args:
        mask: Input mask (modified in-place)
        
    Returns:
        Processed mask
    """
    processor = ConnectedComponentPostprocessor()
    return processor.keep_largest_components_per_label(mask)
