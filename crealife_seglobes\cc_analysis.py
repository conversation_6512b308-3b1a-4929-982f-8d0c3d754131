import functools
from concurrent.futures import ThreadPoolExecutor

import cc3d
import numpy as np
import skimage.measure


def keep_largest_connected_component(mask: np.ndarray, label: int) -> np.ndarray:
    """
    保留给定标签的最大连通域，返回bool
    """
    bn_mask = mask == label
    labeled_mask, num_features = cc3d.connected_components(bn_mask, connectivity=6, return_N=True)
    if num_features == 0:
        return bn_mask
    rg = skimage.measure.regionprops(labeled_mask)
    rg.sort(key=lambda r: r.area, reverse=True)
    rem = np.zeros(mask.shape, dtype=bool)
    rem[rg[0].slice][rg[0].image] = True
    return rem


def _keep_largest_cc_every_label(mask):
    """
    保留给定标签的最大连通域，就地修改
    """
    if mask.size != 0:
        label_max = np.max(mask)
        if label_max != 0:
            klcc = functools.partial(_remove_nonlargest_cc, mask)
            params_list = [lid for lid in range(1, label_max+1)]
            with ThreadPoolExecutor(4) as pool:
                pool.map(klcc, params_list)
    return mask
    # for lid in range(1, label_max+1):
    #     _remove_nonlargest_cc(mask, lid)


def _remove_nonlargest_cc(mask, label_id):
    # bn_mask = mask == label_id
    # fc = cc3d.largest_k(bn_mask, k=1, connectivity=6, delta=0)>0
    # bn_mask = bn_mask - fc
    # mask[bn_mask] = 0
    bn_mask = mask == label_id
    labeled_mask, num_features = cc3d.connected_components(bn_mask, connectivity=6, return_N=True)
    if num_features == 0:
        return bn_mask
    rgs = skimage.measure.regionprops(labeled_mask)
    rgs.sort(key=lambda r: r.area, reverse=True)
    for rg in rgs[1:]:
        mask[rg.slice][rg.image] = 0
