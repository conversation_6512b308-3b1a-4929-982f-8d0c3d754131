import SimpleITK as sitk

from .config import get_cfg_base_params
from .lung_voi import voi_restore
from .post_processing import postprocess_mask_array
from .seg_processer import SegProcessor


def predict_and_process(param_dict, idxs, roi_size, rsp_img, sitkimg, voi_rspimg, additional_dct=None) -> sitk.Image:
    _kwargs = get_cfg_base_params(**param_dict)
    # Merge configurations, with param_dict taking precedence
    merged_config = {**_kwargs, **param_dict}
    model = SegProcessor(**merged_config)
    img = sitk.GetArrayFromImage(voi_rspimg)
    voimsk_arr = model(img)
    del model
    res_voimask_arr = postprocess_mask_array(voimsk_arr)
    rsp_voimsk = sitk.GetImageFromArray(res_voimask_arr)
    rsp_voimsk.CopyInformation(voi_rspimg)
    msk = voi_restore(rsp_voimsk, rsp_img, sitkimg, roi_size, idxs)
    return msk
