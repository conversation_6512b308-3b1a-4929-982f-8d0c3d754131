"""
Base postprocessor implementation.

This module provides base postprocessing functionality that can be
extended for specific medical image segmentation applications.
"""

from typing import Dict, Any, Op<PERSON>, Tu<PERSON>, List
import numpy as np
from scipy import ndimage

from ..core.base_components import BasePostprocessor


class GenericPostprocessor(BasePostprocessor):
    """Generic postprocessor with common segmentation postprocessing operations."""
    
    def __init__(self, **kwargs):
        """
        Initialize generic postprocessor.
        
        Args:
            **kwargs: Configuration parameters including:
                - min_component_size: Minimum size for connected components
                - morphological_operations: List of morphological operations to apply
                - fill_holes: Whether to fill holes in segmentation
                - smooth_boundaries: Whether to smooth segmentation boundaries
        """
        super().__init__(**kwargs)
        
        self.min_component_size = kwargs.get('min_component_size', 0)
        self.morphological_operations = kwargs.get('morphological_operations', [])
        self.fill_holes = kwargs.get('fill_holes', False)
        self.smooth_boundaries = kwargs.get('smooth_boundaries', False)
        
        # Morphological operation parameters
        self.erosion_iterations = kwargs.get('erosion_iterations', 1)
        self.dilation_iterations = kwargs.get('dilation_iterations', 1)
        self.opening_iterations = kwargs.get('opening_iterations', 1)
        self.closing_iterations = kwargs.get('closing_iterations', 1)
    
    def postprocess(self, mask: np.ndarray, **kwargs) -> np.ndarray:
        """
        Postprocess segmentation mask.
        
        Args:
            mask: Input segmentation mask
            **kwargs: Additional parameters
            
        Returns:
            Postprocessed mask
        """
        if mask.size == 0:
            return mask
        
        processed = mask.copy()
        
        # Apply postprocessing pipeline
        processed = self._apply_postprocessing_pipeline(processed, **kwargs)
        
        return processed
    
    def _apply_postprocessing_pipeline(self, mask: np.ndarray, **kwargs) -> np.ndarray:
        """
        Apply the complete postprocessing pipeline.
        
        Args:
            mask: Input mask
            **kwargs: Additional parameters
            
        Returns:
            Postprocessed mask
        """
        processed = mask.copy()
        
        # Step 1: Remove small components
        if self.min_component_size > 0:
            processed = self.remove_small_components(processed, self.min_component_size)
        
        # Step 2: Apply morphological operations
        for operation in self.morphological_operations:
            processed = self.apply_morphological_operation(processed, operation)
        
        # Step 3: Fill holes if requested
        if self.fill_holes:
            processed = self.fill_holes_in_mask(processed)
        
        # Step 4: Smooth boundaries if requested
        if self.smooth_boundaries:
            processed = self.smooth_mask_boundaries(processed)
        
        return processed
    
    def remove_small_components(self, mask: np.ndarray, min_size: int) -> np.ndarray:
        """
        Remove small connected components from mask.
        
        Args:
            mask: Input mask
            min_size: Minimum component size to keep
            
        Returns:
            Mask with small components removed
        """
        if min_size <= 0:
            return mask
        
        # Handle multi-label masks
        if mask.max() > 1:
            processed = np.zeros_like(mask)
            for label in range(1, mask.max() + 1):
                binary_mask = (mask == label)
                cleaned = self._remove_small_components_binary(binary_mask, min_size)
                processed[cleaned] = label
            return processed
        else:
            # Binary mask
            return self._remove_small_components_binary(mask.astype(bool), min_size).astype(mask.dtype)
    
    def _remove_small_components_binary(self, binary_mask: np.ndarray, min_size: int) -> np.ndarray:
        """
        Remove small components from binary mask.
        
        Args:
            binary_mask: Binary input mask
            min_size: Minimum component size
            
        Returns:
            Cleaned binary mask
        """
        try:
            import cc3d
            
            # Use cc3d for efficient connected component analysis
            labeled = cc3d.connected_components(binary_mask, connectivity=6)
            
            # Count component sizes
            unique_labels, counts = np.unique(labeled, return_counts=True)
            
            # Create mask for components to keep
            keep_mask = np.isin(labeled, unique_labels[counts >= min_size])
            
            return keep_mask & binary_mask
            
        except ImportError:
            # Fallback to scipy
            labeled, num_features = ndimage.label(binary_mask)
            
            # Remove small components
            component_sizes = ndimage.sum(binary_mask, labeled, range(num_features + 1))
            too_small = component_sizes < min_size
            remove_pixel = too_small[labeled]
            
            return binary_mask & ~remove_pixel
    
    def apply_morphological_operation(self, mask: np.ndarray, operation: str) -> np.ndarray:
        """
        Apply morphological operation to mask.
        
        Args:
            mask: Input mask
            operation: Operation name ('erosion', 'dilation', 'opening', 'closing')
            
        Returns:
            Processed mask
        """
        if operation == 'erosion':
            return ndimage.binary_erosion(mask, iterations=self.erosion_iterations).astype(mask.dtype)
        elif operation == 'dilation':
            return ndimage.binary_dilation(mask, iterations=self.dilation_iterations).astype(mask.dtype)
        elif operation == 'opening':
            return ndimage.binary_opening(mask, iterations=self.opening_iterations).astype(mask.dtype)
        elif operation == 'closing':
            return ndimage.binary_closing(mask, iterations=self.closing_iterations).astype(mask.dtype)
        else:
            print(f"Unknown morphological operation: {operation}")
            return mask
    
    def fill_holes_in_mask(self, mask: np.ndarray) -> np.ndarray:
        """
        Fill holes in segmentation mask.
        
        Args:
            mask: Input mask
            
        Returns:
            Mask with holes filled
        """
        if mask.max() > 1:
            # Multi-label mask
            processed = np.zeros_like(mask)
            for label in range(1, mask.max() + 1):
                binary_mask = (mask == label)
                filled = ndimage.binary_fill_holes(binary_mask)
                processed[filled] = label
            return processed
        else:
            # Binary mask
            return ndimage.binary_fill_holes(mask).astype(mask.dtype)
    
    def smooth_mask_boundaries(self, mask: np.ndarray, sigma: float = 1.0) -> np.ndarray:
        """
        Smooth mask boundaries using Gaussian filtering.
        
        Args:
            mask: Input mask
            sigma: Gaussian kernel standard deviation
            
        Returns:
            Mask with smoothed boundaries
        """
        if mask.max() > 1:
            # Multi-label mask - smooth each label separately
            processed = np.zeros_like(mask)
            for label in range(1, mask.max() + 1):
                binary_mask = (mask == label).astype(float)
                smoothed = ndimage.gaussian_filter(binary_mask, sigma=sigma)
                processed[smoothed > 0.5] = label
            return processed
        else:
            # Binary mask
            smoothed = ndimage.gaussian_filter(mask.astype(float), sigma=sigma)
            return (smoothed > 0.5).astype(mask.dtype)
    
    def get_largest_component(self, mask: np.ndarray, label: Optional[int] = None) -> np.ndarray:
        """
        Keep only the largest connected component.
        
        Args:
            mask: Input mask
            label: Specific label to process (None for binary mask)
            
        Returns:
            Mask with only largest component
        """
        if label is not None:
            # Process specific label
            binary_mask = (mask == label)
            largest = self._get_largest_component_binary(binary_mask)
            result = mask.copy()
            result[mask == label] = 0
            result[largest] = label
            return result
        else:
            # Process as binary mask
            binary_mask = mask > 0
            largest = self._get_largest_component_binary(binary_mask)
            return (largest * mask.max()).astype(mask.dtype)
    
    def _get_largest_component_binary(self, binary_mask: np.ndarray) -> np.ndarray:
        """
        Get largest component from binary mask.
        
        Args:
            binary_mask: Binary input mask
            
        Returns:
            Binary mask with only largest component
        """
        try:
            import cc3d
            
            labeled = cc3d.connected_components(binary_mask, connectivity=6)
            unique_labels, counts = np.unique(labeled, return_counts=True)
            
            if len(unique_labels) <= 1:  # Only background
                return binary_mask
            
            # Find largest non-background component
            non_bg_indices = unique_labels != 0
            if not np.any(non_bg_indices):
                return binary_mask
            
            largest_label = unique_labels[non_bg_indices][np.argmax(counts[non_bg_indices])]
            return labeled == largest_label
            
        except ImportError:
            # Fallback to scipy
            labeled, num_features = ndimage.label(binary_mask)
            
            if num_features == 0:
                return binary_mask
            
            # Find largest component
            component_sizes = ndimage.sum(binary_mask, labeled, range(1, num_features + 1))
            largest_component = np.argmax(component_sizes) + 1
            
            return labeled == largest_component
    
    def get_postprocessing_info(self) -> Dict[str, Any]:
        """
        Get postprocessing configuration information.
        
        Returns:
            Dictionary containing postprocessing parameters
        """
        return {
            'min_component_size': self.min_component_size,
            'morphological_operations': self.morphological_operations,
            'fill_holes': self.fill_holes,
            'smooth_boundaries': self.smooth_boundaries,
            'erosion_iterations': self.erosion_iterations,
            'dilation_iterations': self.dilation_iterations,
            'opening_iterations': self.opening_iterations,
            'closing_iterations': self.closing_iterations
        }
