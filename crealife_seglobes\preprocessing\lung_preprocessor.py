"""
Lung-specific preprocessor.

This module provides preprocessing specifically optimized for lung CT images,
incorporating the original preprocessing logic from the seglobes project.
"""

from typing import Union, Dict, Any
import numpy as np
import SimpleITK as sitk

from .medical_preprocessor import MedicalImagePreprocessor
from ..config import get_cfg_base_params


class LungPreprocessor(MedicalImagePreprocessor):
    """Preprocessor specifically optimized for lung CT images."""
    
    def __init__(self, **kwargs):
        """
        Initialize lung preprocessor.
        
        Args:
            **kwargs: Configuration parameters
        """
        # Get base configuration for lung processing
        base_config = get_cfg_base_params(**kwargs)
        
        # Set lung-specific defaults
        lung_config = {
            'modality': 'CT',
            'body_region': 'chest',
            'intensities': base_config.get('intensities', {}),
            'normalize_intensity': True,
            'add_channel_dim': True,
            'hu_windowing': None  # Use intensity normalization instead
        }
        
        # Merge configurations
        merged_config = {**kwargs, **lung_config}
        super().__init__(**merged_config)
        
        # Lung-specific parameters from original implementation
        self.patch_size = base_config.get('patch_size', (128, 160, 160))
    
    def preprocess(self, image: Union[sitk.Image, np.ndarray], **kwargs) -> np.ndarray:
        """
        Preprocess lung CT image for segmentation model.
        
        Args:
            image: Input image (SimpleITK Image or numpy array)
            **kwargs: Additional parameters
            
        Returns:
            Preprocessed image as numpy array ready for model inference
        """
        # Apply medical image preprocessing
        processed = super().preprocess(image, **kwargs)
        
        # Apply lung-specific post-processing
        processed = self._apply_lung_specific_processing(processed, **kwargs)
        
        return processed
    
    def _apply_lung_specific_processing(self, image_array: np.ndarray, **kwargs) -> np.ndarray:
        """
        Apply lung-specific processing steps.
        
        Args:
            image_array: Preprocessed image array
            **kwargs: Additional parameters
            
        Returns:
            Lung-specific processed image array
        """
        processed = image_array.copy()
        
        # Ensure correct data type
        processed = processed.astype(np.float32)
        
        # Validate dimensions for lung processing
        if processed.ndim == 4 and processed.shape[0] == 1:
            # Already has channel dimension
            pass
        elif processed.ndim == 3:
            # Add channel dimension
            processed = processed[None]
        else:
            raise ValueError(f"Unexpected image dimensions: {processed.shape}")
        
        return processed
    
    def normalize_lung_intensity(self, image_array: np.ndarray) -> np.ndarray:
        """
        Normalize lung CT intensity using the original normalization function.
        
        This implements the 'nmlsm' function from the original code.
        
        Args:
            image_array: Input lung CT image array
            
        Returns:
            Normalized image array
        """
        # Use the intensity parameters from configuration
        mean = self.intensity_mean
        std = self.intensity_std
        lower = self.intensity_lower
        upper = self.intensity_upper
        
        # Apply the original normalization logic
        normalized = np.clip(image_array, lower, upper)
        normalized = (normalized - mean) / std
        
        return normalized.astype(np.float32)
    
    def prepare_for_inference(self, image_array: np.ndarray) -> np.ndarray:
        """
        Prepare image array for model inference.
        
        This method ensures the image is in the correct format expected
        by the lung segmentation model.
        
        Args:
            image_array: Preprocessed image array
            
        Returns:
            Image array ready for model inference
        """
        # Ensure correct shape and data type
        if image_array.ndim == 3:
            # Add batch and channel dimensions: (D, H, W) -> (1, 1, D, H, W)
            prepared = image_array[None, None]
        elif image_array.ndim == 4 and image_array.shape[0] == 1:
            # Add batch dimension: (1, D, H, W) -> (1, 1, D, H, W)
            prepared = image_array[None]
        elif image_array.ndim == 5:
            # Already has correct dimensions
            prepared = image_array
        else:
            raise ValueError(f"Cannot prepare image with shape {image_array.shape} for inference")
        
        return prepared.astype(np.float32)
    
    def validate_input_size(self, image_array: np.ndarray) -> bool:
        """
        Validate that input image size is suitable for lung processing.
        
        Args:
            image_array: Input image array
            
        Returns:
            True if image size is valid
        """
        if image_array.ndim < 3:
            return False
        
        # Get spatial dimensions (last 3 dimensions)
        spatial_dims = image_array.shape[-3:]
        
        # Check minimum size requirements
        min_size = (10, 64, 64)  # Minimum (depth, height, width)
        if any(spatial_dims[i] < min_size[i] for i in range(3)):
            return False
        
        return True
    
    def get_lung_preprocessing_info(self) -> Dict[str, Any]:
        """
        Get lung-specific preprocessing information.
        
        Returns:
            Dictionary containing lung preprocessing parameters
        """
        info = self.get_modality_info()
        info.update({
            'patch_size': self.patch_size,
            'preprocessing_type': 'lung_ct'
        })
        return info


# Convenience function to maintain backward compatibility
def nmlsm(image_array: np.ndarray, mean: float, std: float, lower: float, upper: float) -> np.ndarray:
    """
    Normalize image using the original nmlsm function logic.
    
    This function maintains backward compatibility with the original implementation.
    
    Args:
        image_array: Input image array
        mean: Target mean
        std: Target standard deviation
        lower: Lower clipping value
        upper: Upper clipping value
        
    Returns:
        Normalized image array
    """
    normalized = np.clip(image_array, lower, upper)
    normalized = (normalized - mean) / std
    return normalized.astype(np.float32)
