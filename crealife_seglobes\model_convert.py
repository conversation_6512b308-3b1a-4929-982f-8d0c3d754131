import os

from devutils.model_convert import onnx2trt

from crealife_seglobes.constants import CUR_DEVICE_TAG, ONNX_MODEL_INFO_MAP

MODEL_DIR = os.path.join(os.path.dirname(__file__), 'resources')


if __name__ == '__main__':
    for k in ONNX_MODEL_INFO_MAP.keys():
        kwargs = ONNX_MODEL_INFO_MAP.get(k, None)
        if kwargs is not None:
            kwargs['onnx_path'] = os.path.join(MODEL_DIR, k + '.onnx')
            kwargs['save_trt_path'] = os.path.join(MODEL_DIR, f'{k}.{CUR_DEVICE_TAG}.trt')
            onnx2trt(**kwargs)
