
import time
from crealife_seglobes.api import segment_from_file

print('Starting quick segmentation test...')
start_time = time.time()

try:
    segment_from_file(
        'crealife_seglobes/demo_data/404868.vol',
        'crealife_seglobes/demo_data/404868_debug.vol',
        {'speed': True, 'step_size': 0.95, 'use_trt': False}
    )
    total_time = time.time() - start_time
    print(f'SUCCESS: Total execution time: {total_time:.2f}s')
except Exception as e:
    total_time = time.time() - start_time
    print(f'ERROR after {total_time:.2f}s: {e}')
    import traceback
    traceback.print_exc()
