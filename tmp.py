import numpy as np
from crealife_seglobes.utils import load_vol

# Check if output file exists and has content
try:
    result = load_vol('crealife_seglobes/demo_data/404868_COMPLETE.vol')
    print(f'Output file shape: {result.shape}')
    print(f'Output file dtype: {result.dtype}')
    print(f'Output file min/max: {result.min():.6f} / {result.max():.6f}')
    print(f'Non-zero elements: {np.count_nonzero(result)}')
    print(f'Unique values: {np.unique(result)[:10]}')  # First 10 unique values
except Exception as e:
    print(f'Error loading output: {e}')

# Also check original input for comparison
try:
    original = load_vol('crealife_seglobes/demo_data/404868.vol')
    print(f'Original input shape: {original.shape}')
    print(f'Original input dtype: {original.dtype}')
    print(f'Original input min/max: {original.min():.6f} / {original.max():.6f}')
except Exception as e:
    print(f'Error loading original: {e}')