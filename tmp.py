import time
from crealife_seglobes.api import segment_from_file

start_time = time.time()
try:
    segment_from_file('crealife_seglobes/demo_data/404868.vol', 'crealife_seglobes/demo_data/404868_broadcast_fix.vol', {'speed': True, 'step_size': 0.95, 'use_trt': False})
    print(f'SUCCESS: Total time {time.time() - start_time:.2f}s')
except Exception as e:
    print(f'ERROR: {e}')
    import traceback
    traceback.print_exc()
