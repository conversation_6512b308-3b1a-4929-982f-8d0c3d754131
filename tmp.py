import numpy as np
from crealife_volume import CDBVolume

# Check if output file exists and has content
try:
    vol = CDBVolume()
    vol.loadVolume('crealife_seglobes/demo_data/404868_onnx.vol')
    result = vol.getCTArr()
    print(f'Output file shape: {result.shape}')
    print(f'Output file dtype: {result.dtype}')
    print(f'Output file min/max: {result.min():.6f} / {result.max():.6f}')
    print(f'Non-zero elements: {np.count_nonzero(result)}')
    print(f'Unique values: {np.unique(result)[:10]}')  # First 10 unique values
    print(f'Value distribution:')
    unique, counts = np.unique(result, return_counts=True)
    for val, count in zip(unique[:10], counts[:10]):
        print(f'  Value {val}: {count} pixels ({count/result.size*100:.2f}%)')
except Exception as e:
    print(f'Error loading output: {e}')

# Also check original input for comparison
try:
    vol_orig = CDBVolume()
    vol_orig.loadVolume('crealife_seglobes/demo_data/404868.vol')
    original = vol_orig.getCTArr()
    print(f'\\nOriginal input shape: {original.shape}')
    print(f'Original input dtype: {original.dtype}')
    print(f'Original input min/max: {original.min():.6f} / {original.max():.6f}')
except Exception as e:
    print(f'Error loading original: {e}')