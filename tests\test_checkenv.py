import unittest
from unittest.mock import patch

from crealife_seglobes.constants import check_trt_env


class TestCheckTrtEnv(unittest.TestCase):

    @patch('builtins.__import__')
    def test_0_check_trt_env_success(self, mock_import):
        print('Running test_check_trt_env_success')
        check_trt_env.cache_clear()
        # 模拟成功导入
        mock_import.side_effect = None
        self.assertTrue(check_trt_env())

    @patch('builtins.__import__')
    def test_1_check_trt_env_failure(self, mock_import):
        print('Running test_check_trt_env_failure')
        # 模拟导入失败
        check_trt_env.cache_clear()
        mock_import.side_effect = ImportError
        self.assertFalse(check_trt_env())
        check_trt_env.cache_clear()
