[build-system]
requires = [
    'setuptools>=68.0',
    'Cython>=3.0',
]
build-backend = 'setuptools.build_meta'

[project]
name = 'crealife_seglobes'
dynamic = ['version', 'readme']
description = 'crealife_seglobes'
requires-python = '>=3.8'
classifiers = [
    'Programming Language :: Python :: 3',
    'Operating System :: OS Independent',
]

[project.scripts]
lobeseg = "crealife_seglobes.run:main"

[tool.setuptools.packages.find]
exclude = ["docs*", "tests*"]

[tool.setuptools.dynamic]
version = {attr = 'crealife_seglobes.__version__'}
readme = {file = ['README.md'], content-type = 'text/markdown'}

[tool.mypy]
ignore_missing_imports = true
