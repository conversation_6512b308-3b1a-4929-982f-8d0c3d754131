"""
Generic pipeline implementation for medical image processing.

This module provides a concrete implementation of the processing pipeline
that coordinates all components to process medical images.
"""

import logging
from typing import Optional, Dict, Any, Union
import SimpleITK as sitk
import numpy as np

from .base_components import (
    BasePipeline,
    BaseVOIExtractor,
    BasePreprocessor,
    BaseModelInference,
    BasePostprocessor
)


logger = logging.getLogger(__name__)


class GenericPipeline(BasePipeline):
    """Generic implementation of medical image processing pipeline."""
    
    def __init__(self, voi_extractor: BaseVOIExtractor,
                 preprocessor: BasePreprocessor,
                 model_inference: BaseModelInference,
                 postprocessor: BasePostprocessor,
                 **kwargs):
        """
        Initialize generic pipeline.
        
        Args:
            voi_extractor: VOI extraction component
            preprocessor: Preprocessing component
            model_inference: Model inference component
            postprocessor: Postprocessing component
            **kwargs: Additional configuration
        """
        super().__init__(voi_extractor, preprocessor, model_inference, postprocessor, **kwargs)
        self.debug = kwargs.get('debug', False)
        
        if not self.validate_components():
            raise ValueError("Invalid pipeline components")
    
    def process(self, image: sitk.Image, **kwargs) -> sitk.Image:
        """
        Process input image through the complete pipeline.
        
        Args:
            image: Input SimpleITK image
            **kwargs: Additional parameters
            
        Returns:
            Processed result image
        """
        try:
            # Step 1: VOI extraction
            if self.debug:
                logger.info("Starting VOI extraction...")
            
            voi_image, resampled_image, indices, roi_size = self.voi_extractor.extract_voi(
                image, **kwargs
            )
            
            # Check if valid VOI was extracted
            if np.any(np.array(roi_size) == 0):
                logger.warning("No valid VOI found, returning empty mask")
                empty_mask = sitk.Image(image.GetSize(), sitk.sitkUInt8)
                empty_mask.CopyInformation(image)
                return empty_mask
            
            if self.debug:
                logger.info(f"VOI extracted with size: {roi_size}")
            
            # Step 2: Preprocessing
            if self.debug:
                logger.info("Starting preprocessing...")
            
            preprocessed_image = self.preprocessor.preprocess(voi_image, **kwargs)
            
            if self.debug:
                logger.info(f"Preprocessed image shape: {preprocessed_image.shape}")
            
            # Step 3: Model inference
            if self.debug:
                logger.info("Starting model inference...")
            
            prediction = self.model_inference.predict(preprocessed_image, **kwargs)
            
            if self.debug:
                logger.info(f"Prediction shape: {prediction.shape}")
            
            # Step 4: Postprocessing
            if self.debug:
                logger.info("Starting postprocessing...")
            
            postprocessed_mask = self.postprocessor.postprocess(prediction, **kwargs)
            
            # Convert back to SimpleITK image
            voi_result = sitk.GetImageFromArray(postprocessed_mask)
            voi_result.CopyInformation(voi_image)
            
            # Step 5: Restore to original image space
            if self.debug:
                logger.info("Restoring to original image space...")
            
            final_result = self.voi_extractor.restore_from_voi(
                voi_result, resampled_image, image, roi_size, indices
            )
            
            if self.debug:
                logger.info("Pipeline processing completed successfully")
            
            return final_result
            
        except Exception as e:
            logger.error(f"Pipeline processing failed: {str(e)}")
            if self.debug:
                raise
            # Return empty mask on error
            empty_mask = sitk.Image(image.GetSize(), sitk.sitkUInt8)
            empty_mask.CopyInformation(image)
            return empty_mask

    def process_image(self, image: Union[sitk.Image, np.ndarray], **kwargs) -> Dict[str, Any]:
        """
        Process image and return detailed results.

        Args:
            image: Input image (SimpleITK Image or numpy array)
            **kwargs: Additional parameters

        Returns:
            Dictionary containing processing results
        """
        # Convert numpy array to SimpleITK image if needed
        if isinstance(image, np.ndarray):
            sitk_image = sitk.GetImageFromArray(image)
        else:
            sitk_image = image

        # Process the image
        result_mask = self.process(sitk_image, **kwargs)

        # Convert result to numpy array
        segmentation = sitk.GetArrayFromImage(result_mask)

        return {
            'segmentation': segmentation,
            'original_image': image,
            'result_mask': result_mask,
            'pipeline_info': self.get_pipeline_info()
        }

    def get_pipeline_info(self) -> Dict[str, Any]:
        """
        Get information about the pipeline configuration.
        
        Returns:
            Dictionary containing pipeline information
        """
        return {
            'voi_extractor': type(self.voi_extractor).__name__,
            'preprocessor': type(self.preprocessor).__name__,
            'model_inference': type(self.model_inference).__name__,
            'postprocessor': type(self.postprocessor).__name__,
            'config': self.config
        }
    
    def set_debug(self, debug: bool) -> None:
        """
        Enable or disable debug mode.
        
        Args:
            debug: Whether to enable debug mode
        """
        self.debug = debug
        if debug:
            logging.basicConfig(level=logging.INFO)
        else:
            logging.basicConfig(level=logging.WARNING)
