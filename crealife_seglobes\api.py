from typing import Optional

import numpy as np
import SimpleITK as sitk
from crealife_volume import CDBVolume
from pdutils.io import read_img

from .image_prediction import predict_and_process
from .lung_voi import get_lung_slices


def segment_from_file(p_in: str, p_out: str, params_dict: Optional[dict] = None):
    sitkimg, _ = read_img(p_in, np.int16)
    msk = segment_from_image(sitkimg, params_dict)
    if (params_dict is not None) and (not params_dict.get('is_nii', False)):
        vol = CDBVolume.genVolumeFromSitkImage(msk)
        vol.enable_compress()
        vol.saveVolume(p_out)
    else:
        if not (p_out.endswith('.nii') or p_out.endswith('.nii.gz') or p_out.endswith('.mhd')):
            p_out += '.nii.gz'
        sitk.WriteImage(msk, p_out)


def segment_from_image(sitkimg: sitk.Image, params_dict: Optional[dict] = None) -> sitk.Image:
    """
    Interface convenient for script call.

    Args:
        sitkimg (sitk.Image): original sitk image
        params_dict (dict): Command line parameters dict

    Returns:
        mask (sitk.Image): Lung lobes mask.

    """
    if params_dict is None:
        params_dict = dict()
    voi_rspimg, rsp_img, idxs, roi_size = get_lung_slices(sitkimg, **params_dict)
    if np.any(np.array(roi_size) == 0):
        msk = sitk.Image(sitkimg.GetSize(), sitk.sitkUInt8)
        msk.CopyInformation(sitkimg)
        return msk
    msk = predict_and_process(params_dict, idxs, roi_size, rsp_img, sitkimg, voi_rspimg)
    return msk
