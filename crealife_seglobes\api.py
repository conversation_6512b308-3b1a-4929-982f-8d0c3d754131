from typing import Optional, Dict, Any, Union

import numpy as np
import SimpleIT<PERSON> as sitk
from crealife_volume import CDBVolume
from pdutils.io import read_img

# Import both old and new implementations for backward compatibility
from .image_prediction import predict_and_process
from .lung_voi import get_lung_slices

# Import new pipeline implementation
from .applications.lung_lobe_segmentation import (
    LungLobeSegmentationPipeline,
    segment_lung_image,
    process_lung_ct_scan
)
from .config import get_cfg_base_params


def segment_from_file(p_in: str, p_out: str, params_dict: Optional[dict] = None, use_new_pipeline: bool = True):
    """
    Segment lung lobes from file.

    Args:
        p_in: Input file path
        p_out: Output file path
        params_dict: Parameters dictionary
        use_new_pipeline: Whether to use the new pipeline architecture
    """
    sitkimg, _ = read_img(p_in, np.int16)
    msk = segment_from_image(sitkimg, params_dict, use_new_pipeline=use_new_pipeline)

    if (params_dict is not None) and (not params_dict.get('is_nii', False)):
        vol = CDBVolume.genVolumeFromSitkImage(msk)
        vol.enable_compress()
        vol.saveVolume(p_out)
    else:
        if not (p_out.endswith('.nii') or p_out.endswith('.nii.gz') or p_out.endswith('.mhd')):
            p_out += '.nii.gz'
        sitk.WriteImage(msk, p_out)


def segment_from_image(sitkimg: sitk.Image, params_dict: Optional[dict] = None, use_new_pipeline: bool = True) -> sitk.Image:
    """
    Interface convenient for script call.

    Args:
        sitkimg (sitk.Image): original sitk image
        params_dict (dict): Command line parameters dict
        use_new_pipeline (bool): Whether to use the new pipeline architecture

    Returns:
        mask (sitk.Image): Lung lobes mask.
    """
    if params_dict is None:
        params_dict = dict()

    if use_new_pipeline:
        # Use new pipeline architecture
        try:
            # Get base configuration
            config = get_cfg_base_params(**params_dict)

            # Create pipeline
            pipeline = LungLobeSegmentationPipeline(config, **params_dict)

            # Process image
            results = pipeline.process_image(sitkimg, **params_dict)

            # Convert segmentation back to SimpleITK image
            segmentation_array = results['segmentation']

            # Create SimpleITK image from array
            msk = sitk.GetImageFromArray(segmentation_array.astype(np.uint8))
            msk.CopyInformation(sitkimg)

            return msk

        except Exception as e:
            print(f"New pipeline failed: {e}. Falling back to original implementation.")
            use_new_pipeline = False

    if not use_new_pipeline:
        # Use original implementation
        voi_rspimg, rsp_img, idxs, roi_size = get_lung_slices(sitkimg, **params_dict)
        if np.any(np.array(roi_size) == 0):
            msk = sitk.Image(sitkimg.GetSize(), sitk.sitkUInt8)
            msk.CopyInformation(sitkimg)
            return msk
        msk = predict_and_process(params_dict, idxs, roi_size, rsp_img, sitkimg, voi_rspimg)
        return msk


# New API functions using the pipeline architecture
def segment_lung_lobes(image: Union[sitk.Image, np.ndarray],
                      config: Optional[Dict[str, Any]] = None,
                      **kwargs) -> Union[sitk.Image, np.ndarray]:
    """
    Segment lung lobes using the new pipeline architecture.

    Args:
        image: Input CT image (SimpleITK Image or numpy array)
        config: Configuration dictionary
        **kwargs: Additional parameters

    Returns:
        Lung lobe segmentation (same type as input)
    """
    # Determine input type
    input_is_sitk = isinstance(image, sitk.Image)

    # Convert to numpy if needed
    if input_is_sitk:
        image_array = sitk.GetArrayFromImage(image)
    else:
        image_array = image

    # Get configuration
    if config is None:
        config = get_cfg_base_params(**kwargs)

    # Segment using new pipeline
    segmentation = segment_lung_image(image_array, config, **kwargs)

    # Convert back to original type
    if input_is_sitk:
        result = sitk.GetImageFromArray(segmentation.astype(np.uint8))
        result.CopyInformation(image)
        return result
    else:
        return segmentation


def process_lung_scan(image: Union[sitk.Image, np.ndarray],
                     config: Optional[Dict[str, Any]] = None,
                     **kwargs) -> Dict[str, Any]:
    """
    Complete processing of lung CT scan with detailed results.

    Args:
        image: Input CT image
        config: Configuration dictionary
        **kwargs: Additional parameters

    Returns:
        Dictionary with complete processing results
    """
    # Get configuration
    if config is None:
        config = get_cfg_base_params(**kwargs)

    # Process using new pipeline
    results = process_lung_ct_scan(image, config, **kwargs)

    return results


def create_lung_pipeline(config: Optional[Dict[str, Any]] = None, **kwargs) -> LungLobeSegmentationPipeline:
    """
    Create a lung lobe segmentation pipeline.

    Args:
        config: Configuration dictionary
        **kwargs: Additional parameters

    Returns:
        Configured lung lobe segmentation pipeline
    """
    if config is None:
        config = get_cfg_base_params(**kwargs)

    return LungLobeSegmentationPipeline(config, **kwargs)


# Backward compatibility aliases
segment_from_sitk_image = segment_from_image
segment_lung_from_file = segment_from_file
