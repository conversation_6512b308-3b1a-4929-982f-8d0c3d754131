"""
Lung-specific VOI extractor.

This module provides lung-specific VOI extraction functionality,
integrating classification-based extraction with lung-specific logic.
"""

import math
from typing import Tuple, Optional
import SimpleITK as sitk
import numpy as np

from .classification_based_extractor import ClassificationBasedVOIExtractor
from ..core.utils import validate_image
from ..config import get_cfg_base_params
from ..constants import HASLUNG_CLS_MN, CUR_DEVICE_TAG
from ..utils import str2bool


class LungVOIExtractor(ClassificationBasedVOIExtractor):
    """Lung-specific VOI extractor using classification model."""
    
    def __init__(self, **kwargs):
        """
        Initialize lung VOI extractor.
        
        Args:
            **kwargs: Configuration parameters
        """
        # Get base configuration
        base_config = get_cfg_base_params(**kwargs)
        
        # Set lung-specific parameters
        lung_config = {
            'model_name': HASLUNG_CLS_MN,
            'model_path': self._get_model_path(base_config.get('pkg', '')),
            'use_trt': str2bool(kwargs.get('use_trt', True)),
            'batch_size': 16,
            'target_size': (128, 128),
            'intensity_range': (-1000, 600),
            'speed': kwargs.get('speed', False)
        }
        
        # Merge configurations
        merged_config = {**kwargs, **lung_config}
        super().__init__(**merged_config)
    
    def _get_model_path(self, pkg_dir: str) -> str:
        """
        Get model path for lung classification.
        
        Args:
            pkg_dir: Package directory containing models
            
        Returns:
            Path to lung classification model
        """
        import os
        
        # Try TensorRT model first if available
        if self.use_trt and CUR_DEVICE_TAG:
            trt_path = os.path.join(pkg_dir, f'{HASLUNG_CLS_MN}.{CUR_DEVICE_TAG}.trt')
            if os.path.exists(trt_path):
                return trt_path
        
        # Fall back to ONNX model
        onnx_path = os.path.join(pkg_dir, f'{HASLUNG_CLS_MN}.onnx')
        return onnx_path
    
    def extract_lung_slices(self, image: sitk.Image, **kwargs) -> Tuple[sitk.Image, sitk.Image, Tuple[int, ...], Tuple[int, ...]]:
        """
        Extract lung slices from CT image.
        
        This is the main interface that replaces the original get_lung_slices function.
        
        Args:
            image: Input SimpleITK image
            **kwargs: Additional parameters
            
        Returns:
            Tuple containing:
            - voi_image: Extracted lung VOI image
            - resampled_image: Resampled full image
            - indices: VOI position indices
            - roi_size: VOI size
        """
        if not validate_image(image):
            raise ValueError("Invalid input image")
        
        # Update configuration with runtime parameters
        self.speed_mode = kwargs.get('speed', self.speed_mode)
        
        # Set target spacing based on speed mode
        if self.speed_mode:
            base_config = get_cfg_base_params(**kwargs)
            self.target_spacing = base_config.get('target_spacing', (2.0, 2.0, 2.0))
        else:
            self.target_spacing = (1.0, 1.0, 1.0)
        
        # Perform VOI extraction
        return self.extract_voi(image, **kwargs)
    
    def get_lung_bounds(self, image: sitk.Image) -> Tuple[int, int]:
        """
        Get lung bounds in z-direction using classification.
        
        Args:
            image: Input SimpleITK image
            
        Returns:
            Tuple of (z_min, z_max) indices
        """
        _, z_min, z_max = self.extract_voi_by_classification(image)
        return z_min, z_max
    
    def validate_lung_voi(self, voi_image: sitk.Image, roi_size: Tuple[int, ...]) -> bool:
        """
        Validate lung VOI extraction result.
        
        Args:
            voi_image: Extracted VOI image
            roi_size: VOI size
            
        Returns:
            True if lung VOI is valid
        """
        # Use base validation
        if not super().validate_voi_result(voi_image, roi_size):
            return False
        
        # Additional lung-specific validation
        # Check minimum size requirements for lung processing
        min_size = (64, 64, 10)  # Minimum reasonable lung VOI size
        if any(roi_size[i] < min_size[i] for i in range(len(min_size))):
            return False
        
        return True
    
    def extract_voi(self, image: sitk.Image, **kwargs) -> Tuple[sitk.Image, sitk.Image, Tuple[int, ...], Tuple[int, ...]]:
        """
        Extract lung VOI with validation.
        
        Args:
            image: Input SimpleITK image
            **kwargs: Additional parameters
            
        Returns:
            Tuple containing VOI extraction results
        """
        # Perform extraction using parent method
        voi_image, resampled_image, indices, roi_size = super().extract_voi(image, **kwargs)
        
        # Validate lung-specific requirements
        if not self.validate_lung_voi(voi_image, roi_size):
            # Return empty VOI if validation fails
            empty_voi = sitk.Image((0, 0, 0), sitk.sitkInt16)
            return empty_voi, resampled_image, (0, 0, 0), (0, 0, 0)
        
        return voi_image, resampled_image, indices, roi_size


# Convenience function to maintain backward compatibility
def get_lung_slices(sitkimg: sitk.Image, **params_dict) -> Tuple[sitk.Image, sitk.Image, Tuple[int, ...], Tuple[int, ...]]:
    """
    Extract lung slices from CT image.
    
    This function maintains backward compatibility with the original API.
    
    Args:
        sitkimg: Input SimpleITK image
        **params_dict: Parameters dictionary
        
    Returns:
        Tuple containing:
        - voi_rspimg: VOI resampled image
        - rsp_img: Full resampled image
        - idxs: VOI indices
        - roi_size: VOI size
    """
    extractor = LungVOIExtractor(**params_dict)
    return extractor.extract_lung_slices(sitkimg, **params_dict)


# Convenience function for VOI restoration
def voi_restore(rsp_voimsk: sitk.Image, rsp_img: sitk.Image, sitkimg: sitk.Image, 
               roi_size: Tuple[int, ...], idxs: Tuple[int, ...]) -> sitk.Image:
    """
    Restore VOI result to original image space.
    
    This function maintains backward compatibility with the original API.
    
    Args:
        rsp_voimsk: VOI mask in resampled space
        rsp_img: Full resampled image
        sitkimg: Original image
        roi_size: VOI size
        idxs: VOI indices
        
    Returns:
        Restored mask in original image space
    """
    extractor = LungVOIExtractor()
    return extractor.restore_from_voi(rsp_voimsk, rsp_img, sitkimg, roi_size, idxs)
