stages:
  - precheck
  - modelconvert
  - build
  - deploy

variables:
  PACKAGE_NAME: 'crealife_seglobes'

include:
  - project: 'yaohy/public-ci-configuration'
    ref: main
    file: '/.gitlab-ci.yml'


cover_report_job:
  before_script:
    - chcp.com 65001
  stage: deploy
  tags:
    - 'win_pack'
  script:
    - |
      write-host "Auto unit test coverage..."
      set-content $env:public\do_test.cmd -Value @'
      call activate ndsb120
      echo coverage run -m unittest tests/test_all.py
      echo coverage report
      exit
      '@

      CMD.EXE /C $env:public\do_test.cmd
      exit $LASTEXITCODE
