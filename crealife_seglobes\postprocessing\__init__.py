"""
Postprocessing components for medical image segmentation.

This module provides various postprocessing strategies for segmentation results,
including connected component analysis, morphological operations, and filtering.
"""

from .base_postprocessor import GenericPostprocessor
from .connected_component_processor import ConnectedComponentPostprocessor
from .morphological_processor import MorphologicalPostprocessor
from .segmentation_postprocessor import SegmentationPostprocessor

__all__ = [
    'GenericPostprocessor',
    'ConnectedComponentPostprocessor',
    'MorphologicalPostprocessor', 
    'SegmentationPostprocessor'
]
